# 🗄️ CAMPMAKER - <PERSON>uia de Migração do Banco de Dados

Este guia te ajuda a migrar seu banco de dados PostgreSQL para outro PC de forma fácil e segura.

## 🎯 Opções Disponíveis

### 1. 📦 **<PERSON><PERSON><PERSON> Completa (RECOMENDADO)**
Cria um pacote completo com tudo que você precisa:

```bash
npm run migrate
```

**O que faz:**
- ✅ Backup completo do banco (SQL + JSON)
- ✅ Copia arquivos essenciais (package.json, schema.prisma, etc.)
- ✅ Cria scripts de setup automático para Windows e Linux/Mac
- ✅ Gera documentação completa
- ✅ Configura .env para novo PC

### 2. 🗄️ **Backup Rápido (FUNCIONA SEMPRE)**
Backup usando Prisma (mais confiável):

```bash
npm run db:backup
```

### 3. 🔧 **Backup Profissional (pg_dump)**
Backup usando ferramentas nativas do PostgreSQL:

```bash
npm run db:backup-pg
```

## 🚀 Como Usar

### Passo 1: No PC Atual (<PERSON><PERSON>r <PERSON>up)

```bash
# Opção mais fácil - cria pacote completo
npm run migrate

# Ou apenas backup do banco
npm run db:backup
```

### Passo 2: No Novo PC (Restaurar)

#### Se usou `npm run migrate`:
1. Copie a pasta `migration-package`
2. Configure PostgreSQL no novo PC
3. Execute o script de setup:
   - **Windows**: `setup-windows.bat`
   - **Linux/Mac**: `./setup-unix.sh`

#### Se usou apenas backup:
1. Configure o projeto no novo PC
2. Restaure o backup:
```bash
npm run db:restore backups/seu-arquivo-backup.sql
```

## 🛠️ Configuração no Novo PC

### Opção A: PostgreSQL Local
1. Instale PostgreSQL
2. Crie banco: `createdb campmaker`
3. Configure `.env`:
```env
DATABASE_URL="postgresql://usuario:senha@localhost:5432/campmaker"
```

### Opção B: Supabase (Mais Fácil)
1. Acesse https://supabase.com
2. Crie novo projeto
3. Copie a Connection String
4. Configure `.env`:
```env
DATABASE_URL="postgresql://postgres:[SUA-SENHA]@db.[PROJETO].supabase.co:5432/postgres"
```

## 📋 Scripts Disponíveis

| Comando | Descrição |
|---------|-----------|
| `npm run migrate` | 🚀 Cria pacote completo de migração (RECOMENDADO) |
| `npm run db:backup` | 📦 Backup rápido usando Prisma (FUNCIONA SEMPRE) |
| `npm run db:restore [arquivo]` | 🔄 Restaura backup rápido |
| `npm run db:backup-full` | 📊 Backup completo com todos os detalhes |
| `npm run db:backup-pg` | 🔧 Backup usando pg_dump (profissional) |
| `npm run db:restore-pg [arquivo]` | ⚙️ Restaura backup pg_dump |

## 🔍 Verificação

Após restaurar, teste se funcionou:

```bash
# Testar conexão
npm run db:test

# Abrir interface visual
npm run db:studio

# Executar aplicação
npm run dev
```

## 🆘 Problemas Comuns

### ❌ "pg_dump: command not found"
**Solução:** Use o backup JavaScript:
```bash
npm run db:backup
```

### ❌ "Erro de conexão com banco"
**Soluções:**
1. Verifique se PostgreSQL está rodando
2. Confirme DATABASE_URL no `.env`
3. Teste: `npm run db:test`

### ❌ "Erro nas migrações"
**Soluções:**
1. Execute: `npm run db:generate`
2. Depois: `npm run db:migrate`
3. Se persistir: `npm run db:push`

### ❌ "Arquivo de backup não encontrado"
**Soluções:**
1. Verifique o caminho do arquivo
2. Liste backups: `ls backups/`
3. Use caminho completo: `npm run db:restore backups/nome-do-arquivo.sql`

## 💡 Dicas Importantes

### ✅ Antes de Migrar
- [ ] Teste se o backup funciona localmente
- [ ] Anote suas configurações (.env)
- [ ] Verifique se todos os dados estão salvos

### ✅ No Novo PC
- [ ] Instale Node.js 18+
- [ ] Configure PostgreSQL ou Supabase
- [ ] Execute `npm install` primeiro
- [ ] Configure `.env` antes de restaurar

### ✅ Após Migrar
- [ ] Teste login na aplicação
- [ ] Verifique se os torneios aparecem
- [ ] Confirme se os times estão corretos
- [ ] Execute `npm run db:studio` para ver os dados

## 🔒 Segurança

⚠️ **IMPORTANTE:**
- Nunca compartilhe arquivos `.env` com senhas
- Mantenha backups em local seguro
- Use senhas fortes no novo banco
- Teste tudo antes de deletar o projeto original

## 📞 Suporte

Se encontrar problemas:

1. **Verifique os logs** de erro detalhados
2. **Consulte este guia** novamente
3. **Teste comandos individuais** para identificar onde falha
4. **Use a opção mais simples** (backup JavaScript) se pg_dump não funcionar

---

**💡 Dica Final:** A opção `npm run migrate` é a mais completa e fácil. Use ela se quiser migrar tudo de uma vez!

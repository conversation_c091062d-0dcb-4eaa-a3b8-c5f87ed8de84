# 📦 CAMPMAKER - Pacote de Migração

Este pacote contém tudo que você precisa para configurar o CAMPMAKER em um novo PC.

## 🚀 Como usar

### 1. Configure o banco de dados
Edite o arquivo `.env` e configure a `DATABASE_URL`:

```env
# PostgreSQL local
DATABASE_URL="postgresql://usuario:senha@localhost:5432/campmaker"

# Ou Supabase (mais fácil)
DATABASE_URL="postgresql://postgres:[SUA-SENHA]@db.[PROJETO].supabase.co:5432/postgres"
```

### 2. Execute o setup

**Windows:**
```bash
setup-windows.bat
```

**Linux/Mac:**
```bash
chmod +x setup-unix.sh
./setup-unix.sh
```

### 3. Teste a aplicação
```bash
npm run dev
```

Acesse: http://localhost:3000

## 📁 Arquivos incluídos

- ✅ `database-backup.sql` - Backup completo do banco
- ✅ `database-backup.json` - Backup em formato JSON
- ✅ `package.json` - Dependências do projeto
- ✅ `prisma/schema.prisma` - Schema do banco
- ✅ Scripts de setup automático

## 🆘 Problemas?

1. **Erro de conexão**: Verifique a DATABASE_URL no .env
2. **Erro nas migrações**: Certifique-se que o PostgreSQL está rodando
3. **Erro no restore**: Verifique se o arquivo database-backup.sql existe

## 💡 Dica

Use Supabase em vez de PostgreSQL local - é mais fácil e não precisa instalar nada!

---
Gerado em: 06/08/2025, 17:31:15

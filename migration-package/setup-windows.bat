@echo off
echo 📦 CAMPMAKER - Setup para Novo PC (Windows)
echo.

echo ⚠️  IMPORTANTE: Configure o arquivo .env antes de continuar!
echo    Edite a DATABASE_URL com suas credenciais do PostgreSQL
echo.
pause

echo 1️⃣  Instalando dependências...
npm install
if %errorlevel% neq 0 (
    echo ❌ Erro ao instalar dependências
    pause
    exit /b 1
)

echo.
echo 2️⃣  Gerando cliente Prisma...
npm run db:generate
if %errorlevel% neq 0 (
    echo ❌ Erro ao gerar cliente Prisma
    echo 💡 Verifique a DATABASE_URL no arquivo .env
    pause
    exit /b 1
)

echo.
echo 3️⃣  Executando migrações...
npm run db:migrate
if %errorlevel% neq 0 (
    echo ❌ Erro nas migrações
    echo 💡 Verifique se o PostgreSQL está rodando
    pause
    exit /b 1
)

echo.
echo 4️⃣  Restaurando backup...
npx tsx scripts/quick-restore.ts database-backup.sql
if %errorlevel% neq 0 (
    echo ❌ Erro ao restaurar backup
    pause
    exit /b 1
)

echo.
echo 🎉 Setup concluído com sucesso!
echo.
echo 📋 Próximos passos:
echo 1. Execute: npm run dev
echo 2. Acesse: http://localhost:3000
echo.
pause

#!/bin/bash
echo "📦 CAMPMAKER - Setup para Novo PC (Linux/Mac)"
echo ""

echo "⚠️  IMPORTANTE: Configure o arquivo .env antes de continuar!"
echo "   Edite a DATABASE_URL com suas credenciais do PostgreSQL"
echo ""
read -p "Pressione Enter para continuar após configurar o .env..."

echo "1️⃣  Instalando dependências..."
npm install
if [ $? -ne 0 ]; then
    echo "❌ Erro ao instalar dependências"
    exit 1
fi

echo ""
echo "2️⃣  Gerando cliente Prisma..."
npm run db:generate
if [ $? -ne 0 ]; then
    echo "❌ Erro ao gerar cliente Prisma"
    echo "💡 Verifique a DATABASE_URL no arquivo .env"
    exit 1
fi

echo ""
echo "3️⃣  Executando migrações..."
npm run db:migrate
if [ $? -ne 0 ]; then
    echo "❌ Erro nas migrações"
    echo "💡 Verifique se o PostgreSQL está rodando"
    exit 1
fi

echo ""
echo "4️⃣  Restaurando backup..."
npx tsx scripts/quick-restore.ts database-backup.sql
if [ $? -ne 0 ]; then
    echo "❌ Erro ao restaurar backup"
    exit 1
fi

echo ""
echo "🎉 Setup concluído com sucesso!"
echo ""
echo "📋 Próximos passos:"
echo "1. Execute: npm run dev"
echo "2. Acesse: http://localhost:3000"
echo ""

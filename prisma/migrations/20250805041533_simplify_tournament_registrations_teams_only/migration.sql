/*
  Warnings:

  - You are about to drop the column `participant_id` on the `tournament_registrations` table. All the data in the column will be lost.
  - You are about to drop the column `participant_type` on the `tournament_registrations` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[tournament_id,team_id]` on the table `tournament_registrations` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `team_id` to the `tournament_registrations` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "public"."tournament_registrations" DROP CONSTRAINT "tournament_registrations_team_fkey";

-- DropForeignKey
ALTER TABLE "public"."tournament_registrations" DROP CONSTRAINT "tournament_registrations_user_fkey";

-- DropIndex
DROP INDEX "public"."tournament_registrations_tournament_id_participant_id_key";

-- AlterTable
ALTER TABLE "public"."tournament_registrations" DROP COLUMN "participant_id",
DROP COLUMN "participant_type",
ADD COLUMN     "team_id" TEXT NOT NULL;

-- DropEnum
DROP TYPE "public"."ParticipantType";

-- CreateIndex
CREATE UNIQUE INDEX "tournament_registrations_tournament_id_team_id_key" ON "public"."tournament_registrations"("tournament_id", "team_id");

-- AddForeignKey
ALTER TABLE "public"."tournament_registrations" ADD CONSTRAINT "tournament_registrations_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."teams"("id") ON DELETE CASCADE ON UPDATE CASCADE;

/*
  Warnings:

  - You are about to drop the column `team_id` on the `player_profiles` table. All the data in the column will be lost.
  - Added the required column `game` to the `teams` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "public"."TeamInviteStatus" AS ENUM ('PENDING', 'ACCEPTED', 'DECLINED', 'EXPIRED');

-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "public"."TournamentStatus" ADD VALUE 'waiting-for-participants';
ALTER TYPE "public"."TournamentStatus" ADD VALUE 'ready-to-start';

-- DropForeignKey
ALTER TABLE "public"."player_profiles" DROP CONSTRAINT "player_profiles_team_id_fkey";

-- AlterTable
ALTER TABLE "public"."player_profiles" DROP COLUMN "team_id";

-- AlterTable
ALTER TABLE "public"."teams" ADD COLUMN     "game" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "public"."tournaments" ADD COLUMN     "bracket_generated" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "current_round" INTEGER NOT NULL DEFAULT 1,
ADD COLUMN     "total_rounds" INTEGER;

-- AlterTable
ALTER TABLE "public"."users" ADD COLUMN     "bio" TEXT,
ADD COLUMN     "company" TEXT,
ADD COLUMN     "socialMedia" JSONB,
ADD COLUMN     "website" TEXT;

-- CreateTable
CREATE TABLE "public"."team_invites" (
    "id" TEXT NOT NULL,
    "team_id" TEXT NOT NULL,
    "player_id" TEXT NOT NULL,
    "invited_by" TEXT NOT NULL,
    "status" "public"."TeamInviteStatus" NOT NULL DEFAULT 'PENDING',
    "message" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "responded_at" TIMESTAMP(3),

    CONSTRAINT "team_invites_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "team_invites_team_id_player_id_key" ON "public"."team_invites"("team_id", "player_id");

-- AddForeignKey
ALTER TABLE "public"."team_invites" ADD CONSTRAINT "team_invites_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."teams"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."team_invites" ADD CONSTRAINT "team_invites_player_id_fkey" FOREIGN KEY ("player_id") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."team_invites" ADD CONSTRAINT "team_invites_invited_by_fkey" FOREIGN KEY ("invited_by") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

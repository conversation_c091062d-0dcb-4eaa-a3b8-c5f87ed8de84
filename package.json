{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "server": "tsx watch server/index.ts", "server:build": "tsc server/index.ts --outDir dist", "setup": "node setup.js", "db:test": "tsx src/lib/test-db.ts", "db:studio": "npx prisma studio", "db:migrate": "npx prisma migrate dev", "db:generate": "npx prisma generate", "db:seed": "tsx src/lib/seed.ts", "db:seed-tournament": "tsx scripts/seed-tournament-test.ts", "db:push": "npx prisma db push", "db:backup": "npx tsx scripts/quick-backup.ts", "db:restore": "npx tsx scripts/quick-restore.ts", "db:backup-full": "npx tsx scripts/backup-database.ts", "db:restore-full": "npx tsx scripts/restore-database.ts", "db:backup-pg": "npx tsx scripts/pg-backup.ts", "db:restore-pg": "npx tsx scripts/pg-restore.ts", "migrate": "npx tsx scripts/quick-migrate.ts", "migrate-full": "npx tsx scripts/migrate-to-new-pc.ts"}, "dependencies": {"@prisma/client": "^6.13.0", "@types/three": "^0.179.0", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^4.18.2", "gsap": "^3.13.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.344.0", "morgan": "^1.10.1", "path-to-regexp": "^6.2.1", "prisma": "^6.13.0", "react": "^18.3.1", "react-dom": "^18.3.1", "three": "^0.179.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "nodemon": "^3.1.10", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "tsx": "^4.20.3", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}
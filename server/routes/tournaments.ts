import express from 'express';
import { prisma } from '../lib/prisma';
import { authenticateToken, requireRole, AuthRequest } from '../middleware/auth';

const router = express.Router();

// GET /api/tournaments - Listar todos os torneios
router.get('/', async (req, res) => {
  try {
    console.log('🏆 GET /api/tournaments chamado');

    const { status, includeCompleted } = req.query;

    // Definir filtros baseados nos parâmetros
    let statusFilter: any = {};

    if (status) {
      statusFilter.status = status;
    } else if (includeCompleted !== 'true') {
      // Por padrão, excluir torneios completados
      statusFilter.status = {
        not: 'COMPLETED'
      };
    }

    const tournaments = await prisma.tournament.findMany({
      where: statusFilter,
      include: {
        organizer: {
          select: {
            id: true,
            name: true,
            email: true,
            company: true,
            website: true,
            socialMedia: true,
            bio: true,
            avatar: true
          }
        },
        prizeDistributions: true,
        registrations: {
          include: {
            team: {
              select: {
                id: true,
                name: true,
                logo: true
              }
            }
          }
        },
        _count: {
          select: { registrations: true }
        }
      },
      orderBy: [
        // Prioridade: inscrições abertas > upcoming > pronto para iniciar
        { status: 'asc' },
        { createdAt: 'desc' }
      ]
    });

    const formattedTournaments = tournaments.map(tournament => ({
      id: tournament.id,
      name: tournament.name,
      game: tournament.game,
      organizerId: tournament.organizerId,
      organizer: tournament.organizer, // ← ADICIONADO!
      description: tournament.description || '',
      format: tournament.format.toLowerCase().replace('_', '-'),
      maxParticipants: tournament.maxParticipants,
      registrationFee: Number(tournament.registrationFee),
      prizePool: Number(tournament.prizePool),
      prizeDistribution: tournament.prizeDistributions.map(pd => ({
        place: pd.place,
        percentage: Number(pd.percentage)
      })),
      rules: tournament.rules,
      platforms: tournament.platforms,
      startDate: tournament.startDate.toISOString(),
      endDate: tournament.endDate.toISOString(),
      registrationDeadline: tournament.registrationDeadline.toISOString(),
      status: tournament.status.toLowerCase().replace(/_/g, '-'),
      participants: tournament.registrations.map(r => ({
        id: r.teamId,
        name: r.team.name,
        logo: r.team.logo,
        registeredAt: r.registeredAt.toISOString()
      })),
      participantCount: tournament._count.registrations,
      bannerUrl: tournament.bannerUrl,
      coverImageUrl: tournament.coverImageUrl,
      createdAt: tournament.createdAt.toISOString()
    }));

    console.log('📊 Exemplo de organizador retornado:', formattedTournaments[0]?.organizer);

    res.json({
      success: true,
      tournaments: formattedTournaments
    });

  } catch (error) {
    console.error('Erro ao buscar torneios:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao buscar torneios'
    });
  }
});

// GET /api/tournaments/completed - Buscar torneios completados
router.get('/completed', async (req, res) => {
  try {
    console.log('🏆 GET /api/tournaments/completed chamado');

    const tournaments = await prisma.tournament.findMany({
      where: {
        status: 'COMPLETED'
      },
      include: {
        organizer: {
          select: {
            id: true,
            name: true,
            email: true,
            company: true,
            website: true,
            socialMedia: true,
            bio: true,
            avatar: true
          }
        },
        prizeDistributions: true,
        registrations: {
          include: {
            team: {
              select: {
                id: true,
                name: true,
                logo: true
              }
            }
          }
        },
        _count: {
          select: { registrations: true }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    const formattedTournaments = tournaments.map(tournament => ({
      id: tournament.id,
      name: tournament.name,
      game: tournament.game,
      organizerId: tournament.organizerId,
      organizer: tournament.organizer,
      description: tournament.description || '',
      format: tournament.format.toLowerCase().replace('_', '-'),
      maxParticipants: tournament.maxParticipants,
      registrationFee: Number(tournament.registrationFee),
      prizePool: Number(tournament.prizePool),
      prizeDistribution: tournament.prizeDistributions.map(pd => ({
        place: pd.place,
        percentage: Number(pd.percentage)
      })),
      rules: tournament.rules,
      platforms: tournament.platforms,
      startDate: tournament.startDate.toISOString(),
      endDate: tournament.endDate.toISOString(),
      registrationDeadline: tournament.registrationDeadline.toISOString(),
      status: tournament.status.toLowerCase().replace(/_/g, '-'),
      participants: tournament.registrations.map(r => ({
        id: r.teamId,
        name: r.team.name,
        logo: r.team.logo,
        registeredAt: r.registeredAt.toISOString()
      })),
      participantCount: tournament._count.registrations,
      bannerUrl: tournament.bannerUrl,
      coverImageUrl: tournament.coverImageUrl,
      createdAt: tournament.createdAt.toISOString()
    }));

    res.json({
      success: true,
      tournaments: formattedTournaments
    });

  } catch (error) {
    console.error('❌ Erro ao buscar torneios completados:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao buscar torneios completados'
    });
  }
});

// POST /api/tournaments - Criar novo torneio
router.post('/', authenticateToken, requireRole(['ORGANIZER']), async (req: AuthRequest, res) => {
  try {
    const {
      name,
      game,
      description,
      format,
      maxParticipants,
      registrationFee,
      prizePool,
      prizeDistribution,
      rules,
      platforms,
      startDate,
      endDate,
      registrationDeadline,
      bannerUrl,
      coverImageUrl
    } = req.body;

    // Validações
    if (!name || !game || !format || !maxParticipants || !startDate || !endDate || !registrationDeadline) {
      return res.status(400).json({
        success: false,
        error: 'Campos obrigatórios não preenchidos'
      });
    }

    const tournament = await prisma.tournament.create({
      data: {
        name,
        game,
        organizerId: req.user!.id,
        description: description || '',
        format: format.toUpperCase().replace('-', '_'),
        maxParticipants: parseInt(maxParticipants),
        registrationFee: parseFloat(registrationFee) || 0,
        prizePool: parseFloat(prizePool) || 0,
        rules: Array.isArray(rules) ? rules.filter(rule => rule.trim() !== '') : [],
        platforms: Array.isArray(platforms) ? platforms.filter(platform => platform.trim() !== '') : [],
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        registrationDeadline: new Date(registrationDeadline),
        status: 'UPCOMING',
        bannerUrl: bannerUrl || null,
        coverImageUrl: coverImageUrl || null,
        prizeDistributions: {
          create: Array.isArray(prizeDistribution) ? prizeDistribution.map(pd => ({
            place: parseInt(pd.place),
            percentage: parseFloat(pd.percentage)
          })) : []
        }
      },
      include: {
        organizer: {
          select: {
            id: true,
            name: true,
            email: true,
            company: true,
            website: true,
            socialMedia: true,
            bio: true,
            avatar: true
          }
        },
        prizeDistributions: true,
        registrations: true
      }
    });

    const formattedTournament = {
      id: tournament.id,
      name: tournament.name,
      game: tournament.game,
      organizerId: tournament.organizerId,
      description: tournament.description || '',
      format: tournament.format.toLowerCase().replace('_', '-'),
      maxParticipants: tournament.maxParticipants,
      registrationFee: Number(tournament.registrationFee),
      prizePool: Number(tournament.prizePool),
      prizeDistribution: tournament.prizeDistributions.map(pd => ({
        place: pd.place,
        percentage: Number(pd.percentage)
      })),
      rules: tournament.rules,
      platforms: tournament.platforms,
      startDate: tournament.startDate.toISOString(),
      endDate: tournament.endDate.toISOString(),
      registrationDeadline: tournament.registrationDeadline.toISOString(),
      status: tournament.status.toLowerCase().replace(/_/g, '-'),
      participants: [],
      bannerUrl: tournament.bannerUrl,
      coverImageUrl: tournament.coverImageUrl,
      createdAt: tournament.createdAt.toISOString()
    };

    res.status(201).json({
      success: true,
      tournament: formattedTournament
    });

  } catch (error) {
    console.error('Erro ao criar torneio:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao criar torneio'
    });
  }
});

// PUT /api/tournaments/:id/images - Atualizar imagens do torneio
router.put('/:id/images', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const { id } = req.params;
    const { bannerUrl, coverImageUrl } = req.body;
    const userId = req.user!.id;

    console.log('🖼️ Atualizando imagens do torneio:', { id, bannerUrl, coverImageUrl });

    // Verificar se o torneio existe e se o usuário é o organizador
    const tournament = await prisma.tournament.findUnique({
      where: { id }
    });

    if (!tournament) {
      return res.status(404).json({
        success: false,
        error: 'Torneio não encontrado'
      });
    }

    if (tournament.organizerId !== userId) {
      return res.status(403).json({
        success: false,
        error: 'Apenas o organizador pode atualizar as imagens do torneio'
      });
    }

    // Atualizar as imagens
    const updatedTournament = await prisma.tournament.update({
      where: { id },
      data: {
        bannerUrl: bannerUrl || null,
        coverImageUrl: coverImageUrl || null
      }
    });

    console.log('✅ Imagens atualizadas:', updatedTournament);

    res.json({
      success: true,
      tournament: {
        id: updatedTournament.id,
        bannerUrl: updatedTournament.bannerUrl,
        coverImageUrl: updatedTournament.coverImageUrl
      },
      message: 'Imagens atualizadas com sucesso!'
    });

  } catch (error) {
    console.error('❌ Erro ao atualizar imagens:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao atualizar imagens'
    });
  }
});

// POST /api/tournaments/:tournamentId/register - Registrar time em torneio
router.post('/:tournamentId/register', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const { tournamentId } = req.params;
    const { teamId } = req.body;
    const userId = req.user!.id;

    // Verificar se o torneio existe
    const tournament = await prisma.tournament.findUnique({
      where: { id: tournamentId },
      include: { registrations: true }
    });

    if (!tournament) {
      return res.status(404).json({
        success: false,
        error: 'Torneio não encontrado'
      });
    }

    // Permitir inscrições enquanto:
    // 1. Torneio não foi iniciado (status não é ONGOING ou COMPLETED)
    // 2. Há vagas disponíveis
    if (['ONGOING', 'COMPLETED', 'CANCELLED'].includes(tournament.status)) {
      return res.status(400).json({
        success: false,
        error: 'Torneio não está mais aberto para inscrições'
      });
    }

    if (tournament.registrations.length >= tournament.maxParticipants) {
      return res.status(400).json({
        success: false,
        error: 'Torneio lotado'
      });
    }

    // Determinar qual time inscrever
    let finalTeamId: string;

    if (teamId) {
      // Se teamId foi fornecido, usar esse time específico
      finalTeamId = teamId;
    } else {
      // Se não foi fornecido, buscar o time do usuário
      const userTeams = await prisma.teamMember.findMany({
        where: { playerId: userId },
        include: { team: true }
      });

      if (userTeams.length === 0) {
        return res.status(400).json({
          success: false,
          error: 'Você precisa estar em um time para se inscrever'
        });
      }

      // Usar o primeiro time (pode ser expandido para permitir escolha)
      finalTeamId = userTeams[0].teamId;
    }

    // Verificar se o time existe e é do jogo correto
    const team = await prisma.team.findUnique({
      where: { id: finalTeamId },
      include: {
        members: true,
        captain: true
      }
    });

    if (!team) {
      return res.status(404).json({
        success: false,
        error: 'Time não encontrado'
      });
    }

    if (team.game !== tournament.game) {
      return res.status(400).json({
        success: false,
        error: `Este time é de ${team.game}, mas o torneio é de ${tournament.game}`
      });
    }

    // Verificar se o usuário é membro do time
    const isMember = team.members.some(member => member.playerId === userId);
    if (!isMember) {
      return res.status(403).json({
        success: false,
        error: 'Você não é membro deste time'
      });
    }

    // Verificar se o usuário é o capitão (apenas capitão pode inscrever o time)
    if (team.captainId !== userId) {
      return res.status(403).json({
        success: false,
        error: 'Apenas o capitão pode inscrever o time em torneios'
      });
    }

    // Verificar se o time já está inscrito
    const existingRegistration = await prisma.tournamentRegistration.findUnique({
      where: {
        tournamentId_teamId: {
          tournamentId,
          teamId: finalTeamId
        }
      }
    });

    if (existingRegistration) {
      return res.status(400).json({
        success: false,
        error: 'Time já inscrito neste torneio'
      });
    }

    // Debug: Log dos dados antes de criar
    console.log('🔍 Dados para inscrição:', {
      tournamentId,
      teamId: finalTeamId,
      teamName: team.name,
      tournamentExists: !!tournament
    });

    // Criar inscrição (agora simplificado para apenas times)
    await prisma.tournamentRegistration.create({
      data: {
        tournamentId,
        teamId: finalTeamId
      }
    });

    res.json({
      success: true,
      message: `Time ${team.name} inscrito com sucesso no torneio!`
    });

  } catch (error) {
    console.error('Erro ao registrar no torneio:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao registrar no torneio'
    });
  }
});

// GET /api/tournaments/my - Torneios do usuário logado
router.get('/my', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const userId = req.user!.id;
    const userType = req.user!.type;

    let tournaments;

    if (userType === 'ORGANIZER') {
      // Torneios organizados pelo usuário
      tournaments = await prisma.tournament.findMany({
        where: { organizerId: userId },
        include: {
          organizer: {
            select: {
              id: true,
              name: true,
              email: true,
              company: true,
              website: true,
              socialMedia: true,
              bio: true,
              avatar: true
            }
          },
          prizeDistributions: true,
          registrations: true,
          _count: { select: { registrations: true } }
        },
        orderBy: { createdAt: 'desc' }
      });
    } else {
      // Torneios em que o jogador está inscrito
      const registrations = await prisma.tournamentRegistration.findMany({
        where: { participantId: userId },
        include: {
          tournament: {
            include: {
              organizer: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  company: true,
                  website: true,
                  socialMedia: true,
                  bio: true,
                  avatar: true
                }
              },
              prizeDistributions: true,
              registrations: true,
              _count: { select: { registrations: true } }
            }
          }
        }
      });
      tournaments = registrations.map(reg => reg.tournament);
    }

    const formattedTournaments = tournaments.map(tournament => ({
      id: tournament.id,
      name: tournament.name,
      game: tournament.game,
      organizerId: tournament.organizerId,
      description: tournament.description || '',
      format: tournament.format.toLowerCase().replace('_', '-'),
      maxParticipants: tournament.maxParticipants,
      registrationFee: Number(tournament.registrationFee),
      prizePool: Number(tournament.prizePool),
      prizeDistribution: tournament.prizeDistributions.map(pd => ({
        place: pd.place,
        percentage: Number(pd.percentage)
      })),
      rules: tournament.rules,
      platforms: tournament.platforms,
      startDate: tournament.startDate.toISOString(),
      endDate: tournament.endDate.toISOString(),
      registrationDeadline: tournament.registrationDeadline.toISOString(),
      status: tournament.status.toLowerCase().replace(/_/g, '-'),
      participants: tournament.registrations.map(r => r.participantId),
      participantCount: tournament._count.registrations,
      bannerUrl: tournament.bannerUrl,
      coverImageUrl: tournament.coverImageUrl,
      createdAt: tournament.createdAt.toISOString()
    }));

    res.json({
      success: true,
      tournaments: formattedTournaments
    });

  } catch (error) {
    console.error('Erro ao buscar torneios do usuário:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao buscar torneios'
    });
  }
});

// POST /api/tournaments/:id/start - Começar torneio e gerar brackets
router.post('/:id/start', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const { id } = req.params;
    const userId = req.user!.id;

    console.log('🚀 Iniciando torneio:', id);

    // Verificar se o usuário é o organizador
    const tournament = await prisma.tournament.findUnique({
      where: { id },
      include: {
        registrations: {
          include: {
            team: {
              include: {
                members: true
              }
            }
          }
        }
      }
    });

    if (!tournament) {
      return res.status(404).json({
        success: false,
        error: 'Torneio não encontrado'
      });
    }

    if (tournament.organizerId !== userId) {
      return res.status(403).json({
        success: false,
        error: 'Apenas o organizador pode iniciar o torneio'
      });
    }

    // Verificar se o torneio pode ser iniciado
    const allowedStatuses = [
      'UPCOMING',
      'REGISTRATION',
      'READY_TO_START',
      'WAITING_FOR_PARTICIPANTS',
      'upcoming',
      'registration',
      'ready-to-start',
      'waiting-for-participants'
    ];

    console.log(`🔍 Status atual do torneio: "${tournament.status}"`);

    if (!allowedStatuses.includes(tournament.status)) {
      console.log(`❌ Status não permite iniciar: ${tournament.status}`);
      return res.status(400).json({
        success: false,
        error: `Torneio não pode ser iniciado. Status atual: ${tournament.status}`
      });
    }

    // Verificar se há times suficientes (mínimo 2)
    const teams = tournament.registrations; // Todas as inscrições são de times
    console.log(`📊 Times encontrados:`, teams.map(t => ({ id: t.teamId, name: t.team?.name })));

    if (teams.length < 2) {
      console.log(`❌ Apenas ${teams.length} times inscritos, mínimo 2 necessário`);
      return res.status(400).json({
        success: false,
        error: 'É necessário pelo menos 2 times para iniciar o torneio'
      });
    }

    console.log(`📊 ${teams.length} times inscritos`);

    // Calcular número de rodadas necessárias
    const totalRounds = Math.ceil(Math.log2(teams.length));
    console.log(`🎯 Total de rodadas: ${totalRounds}`);

    // Gerar brackets
    const matches = generateSingleEliminationBracket(teams, tournament.id);
    console.log(`⚔️ ${matches.length} partidas geradas`);

    // Salvar matches no banco
    await prisma.bracketMatch.createMany({
      data: matches
    });

    // Atualizar status do torneio
    const updatedTournament = await prisma.tournament.update({
      where: { id },
      data: {
        status: 'ONGOING',
        bracketGenerated: true,
        totalRounds,
        currentRound: 1
      },
      include: {
        organizer: {
          select: {
            id: true,
            name: true,
            email: true,
            company: true,
            website: true,
            socialMedia: true,
            bio: true,
            avatar: true
          }
        },
        registrations: {
          include: {
            team: {
              include: {
                members: true
              }
            }
          }
        },
        bracketMatches: {
          orderBy: [
            { round: 'asc' },
            { position: 'asc' }
          ]
        }
      }
    });

    console.log('✅ Torneio iniciado com sucesso!');

    res.json({
      success: true,
      tournament: updatedTournament,
      message: 'Torneio iniciado e brackets gerados com sucesso!'
    });

  } catch (error) {
    console.error('❌ Erro ao iniciar torneio:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao iniciar torneio'
    });
  }
});

// POST /api/tournaments/:id/complete - Finalizar torneio manualmente
router.post('/:id/complete', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const { id } = req.params;
    const userId = req.user!.id;

    console.log('🏆 Finalizando torneio manualmente:', id);

    // Verificar se o usuário é o organizador
    const tournament = await prisma.tournament.findUnique({
      where: { id },
      include: {
        bracketMatches: {
          where: { round: { gte: 1 } },
          orderBy: [{ round: 'desc' }, { position: 'asc' }]
        }
      }
    });

    if (!tournament) {
      return res.status(404).json({
        success: false,
        error: 'Torneio não encontrado'
      });
    }

    if (tournament.organizerId !== userId) {
      return res.status(403).json({
        success: false,
        error: 'Apenas o organizador pode finalizar o torneio'
      });
    }

    // Verificar se o torneio pode ser finalizado
    if (tournament.status === 'COMPLETED') {
      return res.status(400).json({
        success: false,
        error: 'Torneio já foi finalizado'
      });
    }

    if (tournament.status !== 'ONGOING') {
      return res.status(400).json({
        success: false,
        error: 'Apenas torneios em andamento podem ser finalizados'
      });
    }

    // Atualizar status do torneio para COMPLETED
    const updatedTournament = await prisma.tournament.update({
      where: { id },
      data: { status: 'COMPLETED' },
      include: {
        organizer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        bracketMatches: {
          where: { status: 'COMPLETED' },
          orderBy: [{ round: 'desc' }, { position: 'asc' }]
        }
      }
    });

    // Criar achievements para os finalistas se houver partidas finais
    const finalMatches = tournament.bracketMatches.filter(m =>
      m.round === tournament.totalRounds && m.status === 'COMPLETED'
    );

    if (finalMatches.length > 0) {
      await createTournamentAchievements(tournament.id, finalMatches);
    }

    console.log('✅ Torneio finalizado manualmente pelo organizador!');

    res.json({
      success: true,
      tournament: updatedTournament,
      message: 'Torneio finalizado com sucesso!'
    });

  } catch (error) {
    console.error('❌ Erro ao finalizar torneio:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao finalizar torneio'
    });
  }
});

// Função para gerar bracket de eliminação simples
function generateSingleEliminationBracket(teams: any[], tournamentId: string) {
  const matches = [];
  let currentTeams = [...teams];
  let round = 1;
  let position = 1;

  // Embaralhar times aleatoriamente
  currentTeams = shuffleArray(currentTeams);

  console.log(`🎲 Times embaralhados:`, currentTeams.map(t => ({ id: t.teamId, name: t.team?.name })));

  while (currentTeams.length > 1) {
    const roundMatches = [];
    const nextRoundTeams = [];

    console.log(`🏁 Rodada ${round}: ${currentTeams.length} times`);

    // Criar partidas para a rodada atual
    for (let i = 0; i < currentTeams.length; i += 2) {
      const team1 = currentTeams[i];
      const team2 = currentTeams[i + 1] || null; // Pode ser null se número ímpar

      console.log(`⚔️ Partida ${position}: ${team1?.team?.name || 'TBD'} vs ${team2?.team?.name || 'BYE'}`);

      if (!team2) {
        // BYE - time avança automaticamente
        console.log(`🎯 ${team1?.team?.name} avança automaticamente (BYE)`);
        roundMatches.push({
          tournamentId,
          round,
          position: position++,
          participant1: team1?.teamId || null,
          participant2: null,
          winner: team1?.teamId || null,
          status: 'COMPLETED'
        });

        // Time avança automaticamente para próxima rodada
        nextRoundTeams.push(team1);
      } else {
        // Partida normal
        roundMatches.push({
          tournamentId,
          round,
          position: position++,
          participant1: team1?.teamId || null,
          participant2: team2?.teamId || null,
          status: 'PENDING'
        });

        // Para próxima rodada, adicionar placeholder para o vencedor
        nextRoundTeams.push(null); // Será preenchido quando a partida for decidida
      }
    }

    matches.push(...roundMatches);

    // Preparar para próxima rodada
    currentTeams = nextRoundTeams;
    round++;
    position = 1;
  }

  console.log(`✅ ${matches.length} partidas geradas para ${round - 1} rodadas`);
  return matches;
}

// POST /api/tournaments/:tournamentId/matches/:matchId/result - Atualizar resultado de partida
router.post('/:tournamentId/matches/:matchId/result', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const { tournamentId, matchId } = req.params;
    const { winner, scoreP1, scoreP2 } = req.body;
    const userId = req.user!.id;

    console.log('🎯 Atualizando resultado da partida:', { tournamentId, matchId, winner, scoreP1, scoreP2 });

    // Verificar se o usuário é o organizador
    const tournament = await prisma.tournament.findUnique({
      where: { id: tournamentId },
      include: {
        bracketMatches: {
          orderBy: [
            { round: 'asc' },
            { position: 'asc' }
          ]
        }
      }
    });

    if (!tournament) {
      return res.status(404).json({
        success: false,
        error: 'Torneio não encontrado'
      });
    }

    if (tournament.organizerId !== userId) {
      return res.status(403).json({
        success: false,
        error: 'Apenas o organizador pode atualizar resultados'
      });
    }

    // Verificar se a partida existe
    const match = await prisma.bracketMatch.findUnique({
      where: { id: matchId }
    });

    if (!match) {
      return res.status(404).json({
        success: false,
        error: 'Partida não encontrada'
      });
    }

    if (match.status === 'COMPLETED') {
      return res.status(400).json({
        success: false,
        error: 'Partida já foi finalizada'
      });
    }

    // Verificar se a partida tem dois participantes (não é BYE)
    if (!match.participant1 || !match.participant2) {
      return res.status(400).json({
        success: false,
        error: 'Esta partida não pode ser finalizada pois não tem dois participantes. Times avançam automaticamente quando não há adversário.'
      });
    }

    // Validar se o vencedor é um dos participantes
    if (winner !== match.participant1 && winner !== match.participant2) {
      return res.status(400).json({
        success: false,
        error: 'Vencedor deve ser um dos participantes da partida'
      });
    }

    // Validar placar (não pode ser empate)
    const score1 = parseInt(scoreP1);
    const score2 = parseInt(scoreP2);

    if (score1 === score2) {
      return res.status(400).json({
        success: false,
        error: 'Não são permitidos empates. Um time deve vencer.'
      });
    }

    // Validar se o vencedor realmente ganhou no placar
    const winnerIsParticipant1 = winner === match.participant1;
    const winnerIsParticipant2 = winner === match.participant2;

    if (winnerIsParticipant1 && score1 <= score2) {
      return res.status(400).json({
        success: false,
        error: 'O placar não confere com o vencedor selecionado. O vencedor deve ter pontuação maior.'
      });
    }

    if (winnerIsParticipant2 && score2 <= score1) {
      return res.status(400).json({
        success: false,
        error: 'O placar não confere com o vencedor selecionado. O vencedor deve ter pontuação maior.'
      });
    }

    // Atualizar resultado da partida
    const updatedMatch = await prisma.bracketMatch.update({
      where: { id: matchId },
      data: {
        winner,
        scoreP1: parseInt(scoreP1),
        scoreP2: parseInt(scoreP2),
        status: 'COMPLETED'
      }
    });

    console.log('✅ Resultado atualizado:', updatedMatch);

    // Avançar vencedor para próxima rodada
    await advanceWinnerToNextRound(tournament, updatedMatch);

    // Não verificar automaticamente se o torneio terminou
    // O organizador deve finalizar manualmente
    // await checkTournamentCompletion(tournament);

    res.json({
      success: true,
      match: updatedMatch,
      message: 'Resultado atualizado com sucesso!'
    });

  } catch (error) {
    console.error('❌ Erro ao atualizar resultado:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao atualizar resultado'
    });
  }
});

// PUT /api/tournaments/:tournamentId/matches/:matchId/result - Editar resultado de partida
router.put('/:tournamentId/matches/:matchId/result', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const { tournamentId, matchId } = req.params;
    const { winner, scoreP1, scoreP2 } = req.body;
    const userId = req.user!.id;

    console.log('✏️ Editando resultado da partida:', { tournamentId, matchId, winner, scoreP1, scoreP2 });

    // Verificar se o usuário é o organizador
    const tournament = await prisma.tournament.findUnique({
      where: { id: tournamentId }
    });

    if (!tournament) {
      return res.status(404).json({
        success: false,
        error: 'Torneio não encontrado'
      });
    }

    if (tournament.organizerId !== userId) {
      return res.status(403).json({
        success: false,
        error: 'Apenas o organizador pode editar resultados'
      });
    }

    // Verificar se a partida existe
    const match = await prisma.bracketMatch.findUnique({
      where: { id: matchId }
    });

    if (!match) {
      return res.status(404).json({
        success: false,
        error: 'Partida não encontrada'
      });
    }

    // Validações (mesmas do endpoint de criação)
    if (winner !== match.participant1 && winner !== match.participant2) {
      return res.status(400).json({
        success: false,
        error: 'Vencedor deve ser um dos participantes da partida'
      });
    }

    const score1 = parseInt(scoreP1);
    const score2 = parseInt(scoreP2);

    if (score1 === score2) {
      return res.status(400).json({
        success: false,
        error: 'Não são permitidos empates. Um time deve vencer.'
      });
    }

    const winnerIsParticipant1 = winner === match.participant1;
    const winnerIsParticipant2 = winner === match.participant2;

    if (winnerIsParticipant1 && score1 <= score2) {
      return res.status(400).json({
        success: false,
        error: 'O placar não confere com o vencedor selecionado. O vencedor deve ter pontuação maior.'
      });
    }

    if (winnerIsParticipant2 && score2 <= score1) {
      return res.status(400).json({
        success: false,
        error: 'O placar não confere com o vencedor selecionado. O vencedor deve ter pontuação maior.'
      });
    }

    // Atualizar resultado da partida
    const updatedMatch = await prisma.bracketMatch.update({
      where: { id: matchId },
      data: {
        winner,
        scoreP1: score1,
        scoreP2: score2,
        status: 'COMPLETED'
      }
    });

    console.log('✅ Resultado editado:', updatedMatch);

    // Atualizar brackets subsequentes se necessário
    await updateSubsequentBrackets(tournament, updatedMatch);

    res.json({
      success: true,
      match: updatedMatch,
      message: 'Resultado editado com sucesso! Brackets atualizados.'
    });

  } catch (error) {
    console.error('❌ Erro ao editar resultado:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao editar resultado'
    });
  }
});

// GET /api/tournaments/:tournamentId/matches - Buscar partidas do torneio
router.get('/:tournamentId/matches', async (req, res) => {
  try {
    const { tournamentId } = req.params;

    const matches = await prisma.bracketMatch.findMany({
      where: { tournamentId },
      orderBy: [
        { round: 'asc' },
        { position: 'asc' }
      ]
    });

    // Buscar dados dos times separadamente
    const teamIds = new Set<string>();
    matches.forEach(match => {
      if (match.participant1) teamIds.add(match.participant1);
      if (match.participant2) teamIds.add(match.participant2);
      if (match.winner) teamIds.add(match.winner);
    });

    const teams = await prisma.team.findMany({
      where: {
        id: { in: Array.from(teamIds) }
      },
      select: {
        id: true,
        name: true,
        logo: true
      }
    });

    // Mapear teams por ID
    const teamMap = new Map(teams.map(team => [team.id, team]));

    // Adicionar dados dos teams às partidas
    const matchesWithTeams = matches.map(match => ({
      ...match,
      participant1Team: match.participant1 ? teamMap.get(match.participant1) : null,
      participant2Team: match.participant2 ? teamMap.get(match.participant2) : null,
      winnerTeam: match.winner ? teamMap.get(match.winner) : null
    }));

    res.json({
      success: true,
      matches: matchesWithTeams
    });

  } catch (error) {
    console.error('❌ Erro ao buscar partidas:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao buscar partidas'
    });
  }
});

// Função para atualizar brackets subsequentes quando resultado é editado
async function updateSubsequentBrackets(tournament: any, editedMatch: any) {
  try {
    console.log('🔄 Atualizando brackets subsequentes...');

    const currentRound = editedMatch.round;
    const nextRound = currentRound + 1;

    // Se não há próxima rodada, não há nada para atualizar
    if (nextRound > tournament.totalRounds) {
      console.log('📍 Não há próxima rodada para atualizar');
      return;
    }

    // Encontrar a partida da próxima rodada onde o vencedor deveria estar
    const nextPosition = Math.ceil(editedMatch.position / 2);

    const nextMatch = await prisma.bracketMatch.findFirst({
      where: {
        tournamentId: tournament.id,
        round: nextRound,
        position: nextPosition
      }
    });

    if (!nextMatch) {
      console.log('❌ Partida da próxima rodada não encontrada');
      return;
    }

    // Determinar se o vencedor vai para participant1 ou participant2
    const isOddPosition = editedMatch.position % 2 === 1;
    const updateData = isOddPosition
      ? { participant1: editedMatch.winner }
      : { participant2: editedMatch.winner };

    // Atualizar a próxima partida
    await prisma.bracketMatch.update({
      where: { id: nextMatch.id },
      data: updateData
    });

    console.log(`✅ Próxima partida atualizada: ${editedMatch.winner} colocado na posição ${isOddPosition ? 1 : 2}`);

    // Se a próxima partida já foi jogada, precisamos resetá-la e propagar as mudanças
    if (nextMatch.status === 'COMPLETED') {
      console.log('⚠️ Próxima partida já foi jogada, resetando...');

      await prisma.bracketMatch.update({
        where: { id: nextMatch.id },
        data: {
          winner: null,
          scoreP1: null,
          scoreP2: null,
          status: 'PENDING'
        }
      });

      // Recursivamente atualizar partidas subsequentes
      await clearSubsequentMatches(tournament, nextMatch);
    }

  } catch (error) {
    console.error('❌ Erro ao atualizar brackets subsequentes:', error);
  }
}

// Função para limpar partidas subsequentes quando uma partida é resetada
async function clearSubsequentMatches(tournament: any, resetMatch: any) {
  try {
    const currentRound = resetMatch.round;
    const nextRound = currentRound + 1;

    if (nextRound > tournament.totalRounds) {
      return; // Não há próxima rodada
    }

    const nextPosition = Math.ceil(resetMatch.position / 2);
    const isOddPosition = resetMatch.position % 2 === 1;

    const nextMatch = await prisma.bracketMatch.findFirst({
      where: {
        tournamentId: tournament.id,
        round: nextRound,
        position: nextPosition
      }
    });

    if (nextMatch) {
      // Remover o participante da próxima partida
      const updateData = isOddPosition
        ? { participant1: null }
        : { participant2: null };

      await prisma.bracketMatch.update({
        where: { id: nextMatch.id },
        data: {
          ...updateData,
          winner: null,
          scoreP1: null,
          scoreP2: null,
          status: 'PENDING'
        }
      });

      console.log(`🧹 Partida subsequente limpa: rodada ${nextRound}, posição ${nextPosition}`);

      // Recursivamente limpar próximas partidas
      await clearSubsequentMatches(tournament, nextMatch);
    }

  } catch (error) {
    console.error('❌ Erro ao limpar partidas subsequentes:', error);
  }
}

// Função para avançar vencedor para próxima rodada
async function advanceWinnerToNextRound(tournament: any, completedMatch: any) {
  try {
    console.log('🎯 Avançando vencedor para próxima rodada...');

    const currentRound = completedMatch.round;
    const nextRound = currentRound + 1;

    // Verificar se há próxima rodada
    if (nextRound > tournament.totalRounds) {
      console.log('🏆 Torneio finalizado! Não há próxima rodada.');
      return;
    }

    // Encontrar a partida da próxima rodada onde o vencedor deve avançar
    const nextPosition = Math.ceil(completedMatch.position / 2);

    const nextMatch = await prisma.bracketMatch.findFirst({
      where: {
        tournamentId: tournament.id,
        round: nextRound,
        position: nextPosition
      }
    });

    if (!nextMatch) {
      console.log('❌ Partida da próxima rodada não encontrada');
      return;
    }

    // Determinar se o vencedor vai para participant1 ou participant2
    const isOddPosition = completedMatch.position % 2 === 1;
    const updateData = isOddPosition
      ? { participant1: completedMatch.winner }
      : { participant2: completedMatch.winner };

    await prisma.bracketMatch.update({
      where: { id: nextMatch.id },
      data: updateData
    });

    console.log(`✅ Vencedor ${completedMatch.winner} avançou para rodada ${nextRound}, posição ${nextPosition}`);

  } catch (error) {
    console.error('❌ Erro ao avançar vencedor:', error);
  }
}

// Função para verificar se o torneio foi concluído
async function checkTournamentCompletion(tournament: any) {
  try {
    console.log('🔍 Verificando conclusão do torneio...');

    // Buscar dados atualizados do torneio
    const currentTournament = await prisma.tournament.findUnique({
      where: { id: tournament.id },
      select: { totalRounds: true, status: true }
    });

    if (!currentTournament || currentTournament.status === 'COMPLETED') {
      return; // Já finalizado ou não encontrado
    }

    // Verificar se todas as partidas da rodada final (Grand Final) foram concluídas
    const finalRoundMatches = await prisma.bracketMatch.findMany({
      where: {
        tournamentId: tournament.id,
        round: currentTournament.totalRounds // Última rodada = Grand Final
      }
    });

    console.log(`🏁 Verificando rodada final (${currentTournament.totalRounds}): ${finalRoundMatches.length} partidas`);

    const allFinalMatchesCompleted = finalRoundMatches.every(match => match.status === 'COMPLETED');

    if (allFinalMatchesCompleted && finalRoundMatches.length > 0) {
      console.log('🏆 GRAND FINAL CONCLUÍDA! Finalizando torneio...');

      // Atualizar status do torneio para COMPLETED
      await prisma.tournament.update({
        where: { id: tournament.id },
        data: { status: 'COMPLETED' }
      });

      // Criar achievements para os top 3
      await createTournamentAchievements(tournament.id, finalRoundMatches);

      console.log('✅ Torneio oficialmente finalizado! Status: COMPLETED');
    } else {
      console.log(`⏳ Torneio ainda em andamento. Partidas finais: ${finalRoundMatches.filter(m => m.status === 'COMPLETED').length}/${finalRoundMatches.length} concluídas`);
    }

  } catch (error) {
    console.error('❌ Erro ao verificar conclusão do torneio:', error);
  }
}

// Função para criar achievements do torneio
async function createTournamentAchievements(tournamentId: string, finalMatches: any[]) {
  try {
    console.log('🏅 Criando achievements...');

    // Encontrar o campeão (vencedor da final)
    const finalMatch = finalMatches[0]; // Assumindo que há apenas uma final
    const champion = finalMatch.winner;
    const runnerUp = finalMatch.participant1 === champion ? finalMatch.participant2 : finalMatch.participant1;

    // Buscar dados do torneio para o achievement
    const tournamentData = await prisma.tournament.findUnique({
      where: { id: tournamentId },
      select: { name: true }
    });

    // Criar achievement para o campeão
    if (champion && tournamentData) {
      await prisma.achievement.create({
        data: {
          teamId: champion,
          tournamentId,
          tournamentName: tournamentData.name,
          place: 1,
          date: new Date()
        }
      });
      console.log(`🥇 Achievement criado para campeão: ${champion}`);
    }

    // Criar achievement para o vice-campeão
    if (runnerUp && tournamentData) {
      await prisma.achievement.create({
        data: {
          teamId: runnerUp,
          tournamentId,
          tournamentName: tournamentData.name,
          place: 2,
          date: new Date()
        }
      });
      console.log(`🥈 Achievement criado para vice-campeão: ${runnerUp}`);
    }

    // TODO: Implementar lógica para 3º lugar (semifinais)

  } catch (error) {
    console.error('❌ Erro ao criar achievements:', error);
  }
}

// Função para embaralhar array
function shuffleArray(array: any[]) {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

export { router as tournamentRoutes };

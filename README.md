# 🏆 CAMPMAKER - Sistema de Gestão de Campeonatos de E-sports

Sistema completo para organização e gestão de campeonatos de e-sports, desenvolvido com React, TypeScript, Express e PostgreSQL.

## 🚀 Tecnologias Utilizadas

### Frontend
- **React 18** - Biblioteca para interfaces de usuário
- **TypeScript** - Tipagem estática para JavaScript
- **Vite** - Build tool e dev server rápido
- **Tailwind CSS** - Framework CSS utilitário
- **Lucide React** - Ícones modernos

### Backend
- **Express.js** - Framework web para Node.js
- **TypeScript** - Tipagem estática
- **Prisma** - ORM moderno para banco de dados
- **PostgreSQL** - Banco de dados relacional
- **JWT** - Autenticação com tokens
- **bcryptjs** - Hash de senhas

## 📋 Pré-requisitos

Antes de começar, certifique-se de ter instalado:

1. **Node.js** (versão 18 ou superior)
   - Baixe em: https://nodejs.org/
   - Verifique a instalação: `node --version` e `npm --version`

2. **PostgreSQL** (uma das opções):
   - **Opção 1**: PostgreSQL local - https://www.postgresql.org/download/
   - **Opção 2**: Docker - `docker run --name postgres -e POSTGRES_PASSWORD=senha -p 5432:5432 -d postgres`
   - **Opção 3**: Serviço online (Supabase, Railway, etc.) - **RECOMENDADO**

## 🛠️ Instalação e Configuração

### 1. Clone o repositório (se aplicável)
```bash
git clone <url-do-repositorio>
cd campmaker
```

### 2. Instale as dependências
```bash
npm install
```

### 3. Configure o banco de dados

#### Opção A: Usando Supabase (Recomendado)
1. Acesse https://supabase.com e crie uma conta
2. Crie um novo projeto
3. Vá em Settings > Database
4. Copie a Connection String (URI)
5. Cole no arquivo `.env` na variável `DATABASE_URL`

#### Opção B: PostgreSQL Local
1. Instale o PostgreSQL
2. Crie um banco de dados chamado `campmaker`
3. Configure a `DATABASE_URL` no arquivo `.env`

### 4. Configure as variáveis de ambiente
```bash
# O arquivo .env já foi criado com valores padrão
# Edite o arquivo .env e configure:
# - DATABASE_URL com suas credenciais do PostgreSQL
# - JWT_SECRET (opcional, já tem um valor padrão)
```

### 5. Configure o banco de dados
```bash
# Gerar o cliente Prisma
npm run db:generate

# Executar migrações (criar tabelas)
npm run db:migrate

# Popular com dados de teste (opcional)
npm run db:seed
```

## 🚀 Como Executar

### Desenvolvimento
Execute o frontend e backend simultaneamente:

```bash
# Terminal 1 - Backend (Express)
npm run server

# Terminal 2 - Frontend (React)
npm run dev
```

Ou execute cada um separadamente:
- **Backend**: `npm run server` (porta 5000)
- **Frontend**: `npm run dev` (porta 3000)

### Acessar a aplicação
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000/api
- **Health Check**: http://localhost:5000/api/health

## 📊 Scripts Disponíveis

### Frontend
- `npm run dev` - Inicia o servidor de desenvolvimento
- `npm run build` - Build para produção
- `npm run preview` - Preview do build de produção

### Backend
- `npm run server` - Inicia o servidor Express em modo watch
- `npm run server:build` - Build do servidor para produção

### Banco de Dados
- `npm run db:generate` - Gera o cliente Prisma
- `npm run db:migrate` - Executa migrações
- `npm run db:push` - Push do schema para o banco
- `npm run db:studio` - Abre o Prisma Studio (interface visual)
- `npm run db:seed` - Popula o banco com dados de teste

### Outros
- `npm run lint` - Executa o linter ESLint

## 🗄️ Estrutura do Banco de Dados

O sistema possui as seguintes entidades principais:

- **Users** - Usuários (jogadores e organizadores)
- **PlayerProfiles** - Perfis detalhados de jogadores
- **Teams** - Times de e-sports
- **Tournaments** - Campeonatos
- **BracketMatches** - Partidas do bracket
- **Achievements** - Conquistas e premiações
- **Notifications** - Sistema de notificações

## 🔐 Autenticação

O sistema utiliza JWT (JSON Web Tokens) para autenticação:
- Tokens válidos por 7 dias
- Middleware de autenticação para rotas protegidas
- Hash de senhas com bcryptjs

## 🎮 Funcionalidades

### Para Jogadores
- Registro e login
- Criação e gerenciamento de perfil
- Criação e participação em times
- Inscrição em torneios
- Visualização de conquistas

### Para Organizadores
- Registro e login como organizador
- Criação e gerenciamento de torneios
- Configuração de brackets e chaves
- Gerenciamento de premiações
- Sistema de notificações

## 🚨 Solução de Problemas

### Erro de conexão com banco
1. Verifique se o PostgreSQL está rodando
2. Confirme as credenciais na `DATABASE_URL`
3. Execute `npm run db:generate` novamente

### Erro "command not found"
1. Verifique se o Node.js está instalado
2. Execute `npm install` para instalar dependências

### Porta já em uso
1. Mude a porta no arquivo `.env` (PORT=5001)
2. Ou mate o processo: `npx kill-port 5000`

## 📝 Dados de Teste

Após executar `npm run db:seed`, você terá:

**Organizador:**
- Email: <EMAIL>
- Senha: 123456

**Jogadores:**
- Email: <EMAIL> / Senha: 123456
- Email: <EMAIL> / Senha: 123456

## 🤝 Contribuição

1. Faça um fork do projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT.
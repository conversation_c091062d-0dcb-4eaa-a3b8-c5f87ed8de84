const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function createTestData() {
  try {
    console.log('🎮 Criando dados de teste para brackets...\n');

    // Dados dos jogadores com timestamp para emails únicos
    const timestamp = Date.now();
    const playersData = [
      { name: '<PERSON>', email: `alex${timestamp}@test.com`, teamName: 'Thunder Bolts' },
      { name: '<PERSON>', email: `bruno${timestamp}@test.com`, teamName: 'Fire Dragons' },
      { name: '<PERSON>', email: `carlos${timestamp}@test.com`, teamName: 'Storm Eagles' },
      { name: '<PERSON>', email: `diego${timestamp}@test.com`, teamName: 'Lightning Wolves' },
      { name: '<PERSON>', email: `eduardo${timestamp}@test.com`, teamName: 'Frost Tigers' },
      { name: '<PERSON>', email: `felipe${timestamp}@test.com`, teamName: '<PERSON> Hawks' },
      { name: '<PERSON>', email: `gabriel${timestamp}@test.com`, teamName: 'Veno<PERSON>' },
      { name: '<PERSON>', email: `hugo${timestamp}@test.com`, teamName: 'Blaze Lions' }
    ];

    const hashedPassword = await bcrypt.hash('123456', 10);
    const createdPlayers = [];
    const createdTeams = [];

    // Criar jogadores e times
    for (const playerData of playersData) {
      console.log(`👤 Criando jogador: ${playerData.name}`);
      
      // Criar jogador
      const player = await prisma.user.create({
        data: {
          name: playerData.name,
          email: playerData.email,
          password: hashedPassword,
          type: 'PLAYER'
        }
      });
      createdPlayers.push(player);

      console.log(`⚔️ Criando time: ${playerData.teamName}`);
      
      // Criar time
      const team = await prisma.team.create({
        data: {
          name: playerData.teamName,
          game: 'Counter-Strike 2',
          captainId: player.id
        }
      });
      createdTeams.push(team);

      // Adicionar jogador como membro do time
      await prisma.teamMember.create({
        data: {
          teamId: team.id,
          playerId: player.id
        }
      });

      console.log(`✅ ${playerData.name} criado e adicionado ao time ${playerData.teamName}\n`);
    }

    // Buscar um torneio existente para inscrever os times
    const tournaments = await prisma.tournament.findMany({
      where: {
        status: 'REGISTRATION',
        game: 'Counter-Strike 2'
      },
      orderBy: { createdAt: 'desc' }
    });

    let tournament = tournaments[0];

    // Se não houver torneio, criar um
    if (!tournament) {
      console.log('🏆 Criando torneio de teste...');
      
      // Buscar organizador
      const organizer = await prisma.user.findFirst({
        where: { type: 'ORGANIZER' }
      });

      if (!organizer) {
        throw new Error('Nenhum organizador encontrado. Crie um organizador primeiro.');
      }

      tournament = await prisma.tournament.create({
        data: {
          name: 'Copa CS2 - Teste de Brackets',
          game: 'Counter-Strike 2',
          organizerId: organizer.id,
          description: 'Torneio de teste para sistema de brackets com 8 times',
          format: 'SINGLE_ELIMINATION',
          maxParticipants: 8,
          registrationFee: 0,
          prizePool: 5000,
          rules: [
            'Formato: Eliminação Simples',
            'Partidas: BO1 até semifinal, BO3 na final',
            'Mapas: Mirage, Inferno, Dust2, Cache',
            'Horário: A definir pelo organizador'
          ],
          platforms: ['Steam'],
          startDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // Amanhã
          endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 1 semana
          registrationDeadline: new Date(Date.now() + 12 * 60 * 60 * 1000), // 12 horas
          status: 'REGISTRATION'
        }
      });

      // Criar distribuição de prêmios
      await prisma.prizeDistribution.createMany({
        data: [
          { tournamentId: tournament.id, place: 1, percentage: 60 },
          { tournamentId: tournament.id, place: 2, percentage: 30 },
          { tournamentId: tournament.id, place: 3, percentage: 10 }
        ]
      });

      console.log(`✅ Torneio criado: ${tournament.name}\n`);
    }

    console.log(`🏆 Inscrevendo times no torneio: ${tournament.name}`);

    // Inscrever todos os times no torneio
    for (const team of createdTeams) {
      await prisma.tournamentRegistration.create({
        data: {
          tournamentId: tournament.id,
          participantId: team.id,
          participantType: 'TEAM'
        }
      });
      console.log(`✅ Time ${team.name} inscrito no torneio`);
    }

    console.log('\n🎉 DADOS DE TESTE CRIADOS COM SUCESSO!\n');

    // Resumo
    console.log('📊 RESUMO:');
    console.log(`├── 👥 Jogadores criados: ${createdPlayers.length}`);
    console.log(`├── ⚔️ Times criados: ${createdTeams.length}`);
    console.log(`├── 🏆 Torneio: ${tournament.name}`);
    console.log(`└── 📝 Inscrições: ${createdTeams.length} times inscritos\n`);

    console.log('🎮 CREDENCIAIS DE LOGIN:');
    playersData.forEach((player, index) => {
      console.log(`├── ${player.name}: ${player.email} / 123456`);
    });

    console.log('\n🚀 PRÓXIMOS PASSOS:');
    console.log('1. Login como organizador: <EMAIL> / 123456');
    console.log('2. Ir para "Ver Torneios" → Ver Detalhes do torneio');
    console.log('3. Clicar "COMEÇAR TORNEIO" para gerar brackets');
    console.log('4. Ver aba "Competição" para visualizar os confrontos');

  } catch (error) {
    console.error('❌ Erro ao criar dados de teste:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestData();

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixRegistrations() {
  try {
    console.log('🔧 Corrigindo registros de times no torneio...');

    // 1. Buscar o torneio mais recente
    const tournament = await prisma.tournament.findFirst({
      orderBy: { createdAt: 'desc' }
    });

    if (!tournament) {
      console.log('❌ Nenhum torneio encontrado');
      return;
    }

    console.log(`✅ Torneio encontrado: ${tournament.name}`);

    // 2. <PERSON><PERSON> os 8 times mais recentes
    const teams = await prisma.team.findMany({
      orderBy: { createdAt: 'desc' },
      take: 8
    });

    if (teams.length < 8) {
      console.log(`❌ Apenas ${teams.length} times encontrados, precisamos de 8`);
      return;
    }

    console.log(`✅ ${teams.length} times encontrados`);

    // 3. Limpar registros existentes para este torneio
    await prisma.tournamentRegistration.deleteMany({
      where: { tournamentId: tournament.id }
    });
    console.log('🧹 Registros antigos removidos');

    // 4. Usar SQL direto para inserir os registros
    console.log('📝 Inserindo registros dos times...');
    
    for (let i = 0; i < teams.length; i++) {
      const team = teams[i];
      
      // Usar SQL direto para evitar problemas de foreign key
      await prisma.$executeRaw`
        INSERT INTO tournament_registrations (id, tournament_id, participant_id, participant_type, registered_at)
        VALUES (gen_random_uuid(), ${tournament.id}, ${team.id}, 'team', NOW())
      `;
      
      console.log(`✅ Time ${i + 1}/8 registrado: ${team.name}`);
    }

    // 5. Atualizar status do torneio
    await prisma.tournament.update({
      where: { id: tournament.id },
      data: { status: 'READY_TO_START' }
    });

    console.log('🚦 Status do torneio atualizado para READY_TO_START');

    // 6. Verificar registros
    const registrations = await prisma.tournamentRegistration.findMany({
      where: { tournamentId: tournament.id },
      include: {
        team: true
      }
    });

    console.log('\n🎉 REGISTROS CRIADOS COM SUCESSO!');
    console.log(`📊 Total de registros: ${registrations.length}`);
    
    console.log('\n🎯 TIMES REGISTRADOS:');
    registrations.forEach((reg, index) => {
      console.log(`${index + 1}. ${reg.team.name}`);
    });

    console.log('\n📋 RESUMO FINAL:');
    console.log(`👤 Organizador: <EMAIL> / senha: 123456`);
    console.log(`🏆 Torneio: ${tournament.name}`);
    console.log(`🎮 Jogo: ${tournament.game}`);
    console.log(`📊 Formato: ${tournament.format}`);
    console.log(`👥 Times registrados: ${registrations.length}/8`);
    console.log(`💰 Premiação: R$ ${tournament.prizePool}`);
    console.log(`🚦 Status: ${tournament.status}`);

    console.log('\n🚀 COMO TESTAR:');
    console.log('1. Acesse: http://localhost:3000');
    console.log('2. Faça login como organizador:');
    console.log('   Email: <EMAIL>');
    console.log('   Senha: 123456');
    console.log('3. Vá para a seção de torneios');
    console.log('4. Encontre o torneio e clique em "INICIAR TORNEIO"');
    console.log('5. Teste a funcionalidade de brackets!');

  } catch (error) {
    console.error('❌ Erro ao corrigir registros:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar o script
fixRegistrations()
  .then(() => {
    console.log('\n✅ Script executado com sucesso!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Erro no script:', error);
    process.exit(1);
  });

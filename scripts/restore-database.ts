// CAMPMAKER - Script de Restore do Banco de Dados
// Este script restaura um backup do banco de dados
// Execute com: node scripts/restore-database.js [arquivo-backup.sql]

import { prisma } from '../src/lib/prisma.js';
import fs from 'fs';
import path from 'path';

async function restoreDatabase(backupFile) {
  try {
    console.log('🔄 Iniciando restore do banco de dados...\n');

    // Verificar se o arquivo existe
    if (!fs.existsSync(backupFile)) {
      console.error(`❌ Arquivo não encontrado: ${backupFile}`);
      process.exit(1);
    }

    console.log(`📁 Arquivo de backup: ${backupFile}`);

    // Ler o arquivo SQL
    const sqlContent = fs.readFileSync(backupFile, 'utf8');
    
    // Dividir em comandos SQL individuais
    const sqlCommands = sqlContent
      .split('\n')
      .filter(line => line.trim() && !line.trim().startsWith('--'))
      .join('\n')
      .split(';')
      .filter(cmd => cmd.trim());

    console.log(`📊 ${sqlCommands.length} comandos SQL encontrados\n`);

    // Executar comandos SQL
    console.log('🗄️  Executando comandos...');
    
    let successCount = 0;
    let errorCount = 0;

    for (let i = 0; i < sqlCommands.length; i++) {
      const command = sqlCommands[i].trim();
      if (!command) continue;

      try {
        await prisma.$executeRawUnsafe(command);
        successCount++;
        
        // Mostrar progresso a cada 10 comandos
        if ((i + 1) % 10 === 0) {
          console.log(`   ⏳ Processados ${i + 1}/${sqlCommands.length} comandos...`);
        }
      } catch (error) {
        errorCount++;
        console.warn(`   ⚠️  Erro no comando ${i + 1}: ${error.message.substring(0, 100)}...`);
        
        // Se houver muitos erros, parar
        if (errorCount > 5) {
          console.error('\n❌ Muitos erros encontrados. Parando restore.');
          throw new Error('Restore falhou devido a muitos erros');
        }
      }
    }

    console.log('\n✅ Comandos executados!');
    console.log(`   - Sucessos: ${successCount}`);
    console.log(`   - Erros: ${errorCount}`);

    // Verificar dados restaurados
    console.log('\n📊 Verificando dados restaurados...');
    
    const userCount = await prisma.user.count();
    const teamCount = await prisma.team.count();
    const tournamentCount = await prisma.tournament.count();
    const registrationCount = await prisma.tournamentRegistration.count();

    console.log(`   ✅ ${userCount} usuários`);
    console.log(`   ✅ ${teamCount} times`);
    console.log(`   ✅ ${tournamentCount} torneios`);
    console.log(`   ✅ ${registrationCount} inscrições`);

    console.log('\n🎉 Restore concluído com sucesso!');
    console.log('\n📋 Próximos passos:');
    console.log('1. Teste a aplicação: npm run dev');
    console.log('2. Verifique os dados: npm run db:studio');
    console.log('3. Execute testes: npm run db:test');

  } catch (error) {
    console.error('\n❌ Erro durante o restore:', error);
    console.log('\n🔧 Possíveis soluções:');
    console.log('1. Verifique se o banco está rodando');
    console.log('2. Confirme a DATABASE_URL no .env');
    console.log('3. Execute as migrações: npm run db:migrate');
    console.log('4. Tente novamente com um backup mais recente');
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  const backupFile = process.argv[2];
  
  if (!backupFile) {
    console.log('📋 Uso: node scripts/restore-database.js [arquivo-backup.sql]');
    console.log('\n📁 Backups disponíveis:');
    
    const backupDir = 'backups';
    if (fs.existsSync(backupDir)) {
      const files = fs.readdirSync(backupDir)
        .filter(f => f.endsWith('.sql'))
        .sort()
        .reverse(); // Mais recentes primeiro
      
      if (files.length > 0) {
        files.forEach((file, index) => {
          const filePath = path.join(backupDir, file);
          const stats = fs.statSync(filePath);
          console.log(`   ${index + 1}. ${file} (${stats.size} bytes, ${stats.mtime.toLocaleDateString()})`);
        });
        
        console.log(`\n💡 Exemplo: node scripts/restore-database.js backups/${files[0]}`);
      } else {
        console.log('   Nenhum backup encontrado. Execute: node scripts/backup-database.js');
      }
    } else {
      console.log('   Pasta backups não encontrada. Execute: node scripts/backup-database.js');
    }
    
    process.exit(1);
  }
  
  restoreDatabase(backupFile);
}

export { restoreDatabase };

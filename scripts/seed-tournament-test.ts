import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function seedTournamentTest() {
  try {
    console.log('🎮 Criando dados de teste para torneio...');

    // 0. LIMPAR DADOS EXISTENTES
    console.log('🧹 Limpando dados existentes...');
    await prisma.tournamentRegistration.deleteMany();
    await prisma.prizeDistribution.deleteMany();
    await prisma.tournament.deleteMany();
    await prisma.teamMember.deleteMany();
    await prisma.team.deleteMany();
    await prisma.playerProfile.deleteMany();
    await prisma.user.deleteMany();
    console.log('✅ Dados limpos');

    // 1. CRIAR ORGANIZADOR
    console.log('👤 Criando organizador...');
    const hashedPassword = await bcrypt.hash('123456', 10);
    
    const organizer = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: '<PERSON>',
        type: 'ORGANIZER',
        company: 'E-Sports Brasil',
        website: 'https://esportsbrasil.com',
        bio: 'Organizador profissional de torneios de e-sports'
      }
    });
    console.log(`✅ Organizador criado: ${organizer.email}`);

    // 2. CRIAR TORNEIO
    console.log('🏆 Criando torneio...');
    const tournament = await prisma.tournament.create({
      data: {
        name: 'Campeonato CS2 - Eliminação Simples',
        game: 'Counter-Strike 2',
        organizerId: organizer.id,
        description: 'Torneio de CS2 com 8 times em formato de eliminação simples. Pronto para iniciar!',
        format: 'SINGLE_ELIMINATION',
        maxParticipants: 8,
        registrationFee: 0,
        prizePool: 5000,
        rules: [
          'Formato: Melhor de 3 (BO3)',
          'Mapas: Mirage, Inferno, Dust2, Cache, Overpass',
          'Proibido uso de cheats ou exploits',
          'Respeito entre jogadores obrigatório'
        ],
        platforms: ['Steam', 'FACEIT'],
        startDate: new Date('2025-08-10T14:00:00Z'),
        endDate: new Date('2025-08-10T22:00:00Z'),
        registrationDeadline: new Date('2025-08-09T23:59:59Z'),
        status: 'READY_TO_START', // Status pronto para iniciar
        bracketGenerated: false,
        currentRound: 1,
        totalRounds: 3 // Para 8 times: Quartas (4), Semi (2), Final (1)
      }
    });
    console.log(`✅ Torneio criado: ${tournament.name}`);

    // 3. CONFIGURAR DISTRIBUIÇÃO DE PRÊMIOS
    console.log('💰 Configurando premiação...');
    await prisma.prizeDistribution.createMany({
      data: [
        { tournamentId: tournament.id, place: 1, percentage: 50.00 }, // 1º lugar: R$ 2.500
        { tournamentId: tournament.id, place: 2, percentage: 30.00 }, // 2º lugar: R$ 1.500
        { tournamentId: tournament.id, place: 3, percentage: 20.00 }  // 3º lugar: R$ 1.000
      ]
    });

    // 4. CRIAR 8 TIMES
    console.log('👥 Criando 8 times...');
    const teamNames = [
      'Furia Esports',
      'MIBR Gaming',
      'Imperial Esports',
      'paiN Gaming',
      'LOUD Esports',
      'Fluxo Gaming',
      'RED Canids',
      'Sharks Esports'
    ];

    const teams = [];
    for (let i = 0; i < teamNames.length; i++) {
      // Criar capitão para cada time
      const captain = await prisma.user.create({
        data: {
          email: `capitao${i + 1}@campmaker.com`,
          password: hashedPassword,
          name: `Capitão ${teamNames[i]}`,
          type: 'PLAYER'
        }
      });

      // Criar perfil de jogador para o capitão
      await prisma.playerProfile.create({
        data: {
          userId: captain.id,
          preferredGames: ['Counter-Strike 2'],
          rank: 'Global Elite',
          discordTag: `capitao${i + 1}#1234`
        }
      });

      // Criar o time
      const team = await prisma.team.create({
        data: {
          name: teamNames[i],
          game: 'Counter-Strike 2',
          captainId: captain.id
        }
      });

      // Adicionar capitão como membro do time
      await prisma.teamMember.create({
        data: {
          teamId: team.id,
          playerId: captain.id
        }
      });

      // Criar 4 jogadores adicionais para cada time
      for (let j = 1; j <= 4; j++) {
        const player = await prisma.user.create({
          data: {
            email: `jogador${i + 1}_${j}@campmaker.com`,
            password: hashedPassword,
            name: `Jogador ${j} - ${teamNames[i]}`,
            type: 'PLAYER'
          }
        });

        await prisma.playerProfile.create({
          data: {
            userId: player.id,
            preferredGames: ['Counter-Strike 2'],
            rank: ['Global Elite', 'Supreme', 'Legendary Eagle'][j % 3],
            discordTag: `player${i + 1}_${j}#${1000 + j}`
          }
        });

        await prisma.teamMember.create({
          data: {
            teamId: team.id,
            playerId: player.id
          }
        });
      }

      teams.push(team);
      console.log(`✅ Time criado: ${team.name} (5 jogadores)`);
    }

    // 5. REGISTRAR TODOS OS TIMES NO TORNEIO
    console.log('📝 Registrando times no torneio...');

    // Registrar um por vez para evitar problemas de foreign key
    for (let i = 0; i < teams.length; i++) {
      const team = teams[i];
      try {
        await prisma.tournamentRegistration.create({
          data: {
            tournamentId: tournament.id,
            participantId: team.id,
            participantType: 'TEAM'
          }
        });
        console.log(`✅ Time ${i + 1}/8 registrado: ${team.name}`);
      } catch (error) {
        console.error(`❌ Erro ao registrar ${team.name}:`, error);
        // Continuar com os próximos times
      }
    }

    console.log('\n🎉 DADOS DE TESTE CRIADOS COM SUCESSO!');
    console.log('\n📋 RESUMO:');
    console.log(`👤 Organizador: ${organizer.email} / senha: 123456`);
    console.log(`🏆 Torneio: ${tournament.name}`);
    console.log(`🎮 Jogo: ${tournament.game}`);
    console.log(`📊 Formato: Eliminação Simples`);
    console.log(`👥 Times: ${teams.length} times registrados`);
    console.log(`💰 Premiação: R$ ${tournament.prizePool}`);
    console.log(`📅 Data: ${tournament.startDate.toLocaleDateString('pt-BR')}`);
    console.log(`🚦 Status: ${tournament.status} (Pronto para iniciar!)`);
    
    console.log('\n🎯 TIMES REGISTRADOS:');
    teams.forEach((team, index) => {
      console.log(`${index + 1}. ${team.name}`);
    });

    console.log('\n🚀 COMO TESTAR:');
    console.log('1. Acesse: http://localhost:3000');
    console.log('2. Faça login como organizador:');
    console.log('   Email: <EMAIL>');
    console.log('   Senha: 123456');
    console.log('3. Vá para a seção de torneios');
    console.log('4. Encontre o torneio "Campeonato CS2 - Eliminação Simples"');
    console.log('5. Clique em "INICIAR TORNEIO" para gerar o bracket');
    console.log('6. Teste a funcionalidade de gerenciamento de partidas');

  } catch (error) {
    console.error('❌ Erro ao criar dados de teste:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Executar o script
seedTournamentTest()
  .then(() => {
    console.log('\n✅ Script executado com sucesso!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Erro no script:', error);
    process.exit(1);
  });

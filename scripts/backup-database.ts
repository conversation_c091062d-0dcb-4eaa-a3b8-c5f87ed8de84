// CAMPMAKER - Script de Backup do Banco de Dados
// Este script cria um backup completo dos dados do PostgreSQL
// Execute com: node scripts/backup-database.js

import { prisma } from '../src/lib/prisma.js';
import fs from 'fs';
import path from 'path';

async function backupDatabase() {
  try {
    console.log('🗄️  Iniciando backup do banco de dados...\n');

    // Criar pasta de backups se não existir
    const backupDir = 'backups';
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir);
    }

    // Nome do arquivo com timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFile = path.join(backupDir, `campmaker-backup-${timestamp}.sql`);

    console.log(`📁 Arquivo de backup: ${backupFile}\n`);

    // Buscar todos os dados
    console.log('📊 Coletando dados...');
    
    const users = await prisma.user.findMany({
      include: {
        playerProfile: true,
        organizedTournaments: true,
        teamMemberships: {
          include: {
            team: true
          }
        }
      }
    });
    console.log(`   ✅ ${users.length} usuários`);

    const teams = await prisma.team.findMany({
      include: {
        members: {
          include: {
            player: true
          }
        }
      }
    });
    console.log(`   ✅ ${teams.length} times`);

    const tournaments = await prisma.tournament.findMany({
      include: {
        prizeDistributions: true,
        registrations: true,
        bracketMatches: true
      }
    });
    console.log(`   ✅ ${tournaments.length} torneios`);

    // Gerar SQL de inserção
    let sqlContent = `-- CAMPMAKER - Backup do Banco de Dados
-- Gerado em: ${new Date().toISOString()}
-- 
-- INSTRUÇÕES PARA RESTAURAR:
-- 1. Certifique-se de que o banco está limpo (execute: npm run db:migrate)
-- 2. Execute este arquivo SQL no PostgreSQL
-- 3. Ou use o script: node scripts/restore-database.js [arquivo]

-- Limpar dados existentes
DELETE FROM tournament_registrations;
DELETE FROM bracket_matches;
DELETE FROM prize_distributions;
DELETE FROM tournaments;
DELETE FROM team_members;
DELETE FROM player_profiles;
DELETE FROM teams;
DELETE FROM users;

-- Resetar sequências
ALTER SEQUENCE IF EXISTS users_id_seq RESTART WITH 1;
ALTER SEQUENCE IF EXISTS teams_id_seq RESTART WITH 1;
ALTER SEQUENCE IF EXISTS tournaments_id_seq RESTART WITH 1;

`;

    // Inserir usuários
    if (users.length > 0) {
      sqlContent += '\n-- USUÁRIOS\n';
      for (const user of users) {
        const escapedName = user.name.replace(/'/g, "''");
        const escapedEmail = user.email.replace(/'/g, "''");
        const escapedPassword = user.password.replace(/'/g, "''");
        const avatar = user.avatar ? `'${user.avatar.replace(/'/g, "''")}'` : 'NULL';
        
        sqlContent += `INSERT INTO users (id, email, password, name, type, avatar, created_at, updated_at) VALUES ('${user.id}', '${escapedEmail}', '${escapedPassword}', '${escapedName}', '${user.type}', ${avatar}, '${user.createdAt.toISOString()}', '${user.updatedAt.toISOString()}');\n`;
      }
    }

    // Inserir times
    if (teams.length > 0) {
      sqlContent += '\n-- TIMES\n';
      for (const team of teams) {
        const escapedName = team.name.replace(/'/g, "''");
        const escapedGame = team.game.replace(/'/g, "''");
        const description = team.description ? `'${team.description.replace(/'/g, "''")}'` : 'NULL';
        const logo = team.logo ? `'${team.logo.replace(/'/g, "''")}'` : 'NULL';
        
        sqlContent += `INSERT INTO teams (id, name, game, description, logo, captain_id, created_at) VALUES ('${team.id}', '${escapedName}', '${escapedGame}', ${description}, ${logo}, '${team.captainId}', '${team.createdAt.toISOString()}');\n`;
      }
    }

    // Inserir perfis de jogadores
    const playerProfiles = users.filter(u => u.playerProfile).map(u => u.playerProfile);
    if (playerProfiles.length > 0) {
      sqlContent += '\n-- PERFIS DE JOGADORES\n';
      for (const profile of playerProfiles) {
        const peripherals = profile.peripherals ? `'${profile.peripherals.replace(/'/g, "''")}'` : 'NULL';
        const preferredGames = profile.preferredGames.length > 0 ? `ARRAY[${profile.preferredGames.map(g => `'${g.replace(/'/g, "''")}'`).join(', ')}]` : 'ARRAY[]::text[]';
        const rank = profile.rank ? `'${profile.rank.replace(/'/g, "''")}'` : 'NULL';
        const twitchUrl = profile.twitchUrl ? `'${profile.twitchUrl.replace(/'/g, "''")}'` : 'NULL';
        const youtubeUrl = profile.youtubeUrl ? `'${profile.youtubeUrl.replace(/'/g, "''")}'` : 'NULL';
        const discordTag = profile.discordTag ? `'${profile.discordTag.replace(/'/g, "''")}'` : 'NULL';
        const teamId = profile.teamId ? `'${profile.teamId}'` : 'NULL';
        
        sqlContent += `INSERT INTO player_profiles (user_id, age, peripherals, preferred_games, rank, twitch_url, youtube_url, discord_tag, team_id, created_at) VALUES ('${profile.userId}', ${profile.age || 'NULL'}, ${peripherals}, ${preferredGames}, ${rank}, ${twitchUrl}, ${youtubeUrl}, ${discordTag}, ${teamId}, '${profile.createdAt.toISOString()}');\n`;
      }
    }

    // Inserir membros de times
    const teamMembers = [];
    for (const team of teams) {
      for (const member of team.members) {
        teamMembers.push(member);
      }
    }
    
    if (teamMembers.length > 0) {
      sqlContent += '\n-- MEMBROS DE TIMES\n';
      for (const member of teamMembers) {
        sqlContent += `INSERT INTO team_members (team_id, player_id, role, joined_at) VALUES ('${member.teamId}', '${member.playerId}', '${member.role}', '${member.joinedAt.toISOString()}');\n`;
      }
    }

    // Inserir torneios
    if (tournaments.length > 0) {
      sqlContent += '\n-- TORNEIOS\n';
      for (const tournament of tournaments) {
        const escapedName = tournament.name.replace(/'/g, "''");
        const escapedGame = tournament.game.replace(/'/g, "''");
        const description = tournament.description ? `'${tournament.description.replace(/'/g, "''")}'` : 'NULL';
        const rules = tournament.rules.length > 0 ? `ARRAY[${tournament.rules.map(r => `'${r.replace(/'/g, "''")}'`).join(', ')}]` : 'ARRAY[]::text[]';
        const platforms = tournament.platforms.length > 0 ? `ARRAY[${tournament.platforms.map(p => `'${p.replace(/'/g, "''")}'`).join(', ')}]` : 'ARRAY[]::text[]';
        
        sqlContent += `INSERT INTO tournaments (id, name, game, organizer_id, description, format, max_participants, registration_fee, prize_pool, rules, platforms, start_date, end_date, registration_deadline, status, created_at, updated_at) VALUES ('${tournament.id}', '${escapedName}', '${escapedGame}', '${tournament.organizerId}', ${description}, '${tournament.format}', ${tournament.maxParticipants}, ${tournament.registrationFee}, ${tournament.prizePool}, ${rules}, ${platforms}, '${tournament.startDate.toISOString()}', '${tournament.endDate.toISOString()}', '${tournament.registrationDeadline.toISOString()}', '${tournament.status}', '${tournament.createdAt.toISOString()}', '${tournament.updatedAt.toISOString()}');\n`;
      }
    }

    // Inserir distribuições de prêmios
    const prizeDistributions = [];
    for (const tournament of tournaments) {
      prizeDistributions.push(...tournament.prizeDistributions);
    }
    
    if (prizeDistributions.length > 0) {
      sqlContent += '\n-- DISTRIBUIÇÕES DE PRÊMIOS\n';
      for (const prize of prizeDistributions) {
        sqlContent += `INSERT INTO prize_distributions (id, tournament_id, place, percentage) VALUES ('${prize.id}', '${prize.tournamentId}', ${prize.place}, ${prize.percentage});\n`;
      }
    }

    // Inserir registros de torneios
    const registrations = [];
    for (const tournament of tournaments) {
      registrations.push(...tournament.registrations);
    }
    
    if (registrations.length > 0) {
      sqlContent += '\n-- REGISTROS DE TORNEIOS\n';
      for (const reg of registrations) {
        sqlContent += `INSERT INTO tournament_registrations (id, tournament_id, participant_id, participant_type, registered_at) VALUES ('${reg.id}', '${reg.tournamentId}', '${reg.participantId}', '${reg.participantType}', '${reg.registeredAt.toISOString()}');\n`;
      }
    }

    // Inserir partidas do bracket
    const bracketMatches = [];
    for (const tournament of tournaments) {
      bracketMatches.push(...tournament.bracketMatches);
    }
    
    if (bracketMatches.length > 0) {
      sqlContent += '\n-- PARTIDAS DO BRACKET\n';
      for (const match of bracketMatches) {
        const player1Id = match.player1Id ? `'${match.player1Id}'` : 'NULL';
        const player2Id = match.player2Id ? `'${match.player2Id}'` : 'NULL';
        const winnerId = match.winnerId ? `'${match.winnerId}'` : 'NULL';
        const player1Score = match.player1Score || 'NULL';
        const player2Score = match.player2Score || 'NULL';
        
        sqlContent += `INSERT INTO bracket_matches (id, tournament_id, round, match_number, player1_id, player2_id, winner_id, player1_score, player2_score, status, scheduled_at, completed_at) VALUES ('${match.id}', '${match.tournamentId}', ${match.round}, ${match.matchNumber}, ${player1Id}, ${player2Id}, ${winnerId}, ${player1Score}, ${player2Score}, '${match.status}', ${match.scheduledAt ? `'${match.scheduledAt.toISOString()}'` : 'NULL'}, ${match.completedAt ? `'${match.completedAt.toISOString()}'` : 'NULL'});\n`;
      }
    }

    sqlContent += '\n-- Backup concluído com sucesso!\n';

    // Salvar arquivo
    fs.writeFileSync(backupFile, sqlContent);

    console.log('\n🎉 Backup criado com sucesso!');
    console.log(`📁 Arquivo: ${backupFile}`);
    console.log(`📊 Estatísticas:`);
    console.log(`   - ${users.length} usuários`);
    console.log(`   - ${teams.length} times`);
    console.log(`   - ${tournaments.length} torneios`);
    console.log(`   - ${registrations.length} inscrições`);
    console.log(`   - ${bracketMatches.length} partidas`);
    
    console.log('\n📋 Para usar em outro PC:');
    console.log('1. Copie o arquivo de backup');
    console.log('2. Configure o projeto no outro PC');
    console.log('3. Execute: node scripts/restore-database.js [arquivo-backup]');
    console.log('4. Ou execute o SQL diretamente no PostgreSQL');

  } catch (error) {
    console.error('❌ Erro ao criar backup:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar se chamado diretamente
if (require.main === module || import.meta.url === `file://${process.argv[1]}`) {
  backupDatabase().catch(console.error);
}

export { backupDatabase };

// CAMPMAKER - Script de Backup Simples
// Execute com: npx tsx scripts/simple-backup.ts

import { prisma } from '../src/lib/prisma.js';
import fs from 'fs';

async function simpleBackup() {
  console.log('🗄️  Iniciando backup simples...');
  
  try {
    // Testar conexão
    await prisma.$connect();
    console.log('✅ Conectado ao banco');

    // Buscar dados
    const users = await prisma.user.findMany();
    const teams = await prisma.team.findMany();
    const tournaments = await prisma.tournament.findMany();

    console.log(`📊 Dados encontrados:`);
    console.log(`   - ${users.length} usuários`);
    console.log(`   - ${teams.length} times`);
    console.log(`   - ${tournaments.length} torneios`);

    // Criar pasta de backups
    if (!fs.existsSync('backups')) {
      fs.mkdirSync('backups');
    }

    // Criar backup JSON simples
    const backupData = {
      users,
      teams,
      tournaments,
      timestamp: new Date().toISOString()
    };

    const filename = `backups/simple-backup-${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
    fs.writeFileSync(filename, JSON.stringify(backupData, null, 2));

    console.log(`✅ Backup criado: ${filename}`);
    console.log(`📁 Tamanho: ${(fs.statSync(filename).size / 1024).toFixed(1)} KB`);

  } catch (error) {
    console.error('❌ Erro:', error);
  } finally {
    await prisma.$disconnect();
  }
}

simpleBackup();

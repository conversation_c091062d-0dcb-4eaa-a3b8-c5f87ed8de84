-- Script para corrigir registros de times no torneio
-- Execute este script no PostgreSQL para registrar os times no torneio

-- <PERSON><PERSON>, vamos verificar se temos um torneio e times criados
SELECT 'Torneios disponíveis:' as info;
SELECT id, name, status FROM tournaments ORDER BY created_at DESC LIMIT 5;

SELECT 'Times disponíveis:' as info;
SELECT id, name FROM teams ORDER BY created_at DESC LIMIT 10;

-- Inserir registros dos times no torneio (assumindo que temos 8 times e 1 torneio)
-- Vamos usar os IDs mais recentes
INSERT INTO tournament_registrations (id, tournament_id, participant_id, participant_type, registered_at)
SELECT 
    gen_random_uuid() as id,
    (SELECT id FROM tournaments ORDER BY created_at DESC LIMIT 1) as tournament_id,
    t.id as participant_id,
    'team' as participant_type,
    NOW() as registered_at
FROM (
    SELECT id FROM teams ORDER BY created_at DESC LIMIT 8
) t
ON CONFLICT (tournament_id, participant_id) DO NOTHING;

-- Verificar se os registros foram inseridos
SELECT 'Registros inseridos:' as info;
SELECT 
    tr.id,
    t.name as tournament_name,
    tm.name as team_name,
    tr.participant_type,
    tr.registered_at
FROM tournament_registrations tr
JOIN tournaments t ON tr.tournament_id = t.id
JOIN teams tm ON tr.participant_id = tm.id
ORDER BY tr.registered_at DESC;

-- Atualizar status do torneio para READY_TO_START
UPDATE tournaments 
SET status = 'ready-to-start'
WHERE id = (SELECT id FROM tournaments ORDER BY created_at DESC LIMIT 1);

SELECT 'Status do torneio atualizado:' as info;
SELECT id, name, status FROM tournaments ORDER BY created_at DESC LIMIT 1;

// CAMPMAKER - Script de Restore usando psql (PostgreSQL nativo)
// Este script restaura backups criados com pg_dump
// Execute com: node scripts/pg-restore.js [arquivo-backup.sql]

import { exec } from 'child_process';
import fs from 'fs';
import path from 'path';
import { promisify } from 'util';

const execAsync = promisify(exec);

// Função para extrair informações da DATABASE_URL
function parseDatabaseUrl(url) {
  const regex = /postgresql:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/(.+)/;
  const match = url.match(regex);
  
  if (!match) {
    throw new Error('Formato de DATABASE_URL inválido');
  }
  
  return {
    user: match[1],
    password: match[2],
    host: match[3],
    port: match[4],
    database: match[5]
  };
}

async function restorePgBackup(backupFile) {
  try {
    console.log('🔄 Iniciando restore com psql...\n');

    // Verificar se o arquivo existe
    if (!fs.existsSync(backupFile)) {
      console.error(`❌ Arquivo não encontrado: ${backupFile}`);
      process.exit(1);
    }

    const stats = fs.statSync(backupFile);
    console.log(`📁 Arquivo de backup: ${backupFile}`);
    console.log(`📊 Tamanho: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
    console.log(`🕒 Modificado em: ${stats.mtime.toLocaleString()}\n`);

    // Ler DATABASE_URL do .env
    if (!fs.existsSync('.env')) {
      console.error('❌ Arquivo .env não encontrado!');
      console.log('💡 Copie o .env.example e configure suas variáveis');
      process.exit(1);
    }

    const envContent = fs.readFileSync('.env', 'utf8');
    const databaseUrlMatch = envContent.match(/DATABASE_URL="([^"]+)"/);
    
    if (!databaseUrlMatch) {
      throw new Error('DATABASE_URL não encontrada no arquivo .env');
    }
    
    const databaseUrl = databaseUrlMatch[1];
    const dbConfig = parseDatabaseUrl(databaseUrl);
    
    console.log(`📊 Conectando ao banco: ${dbConfig.host}:${dbConfig.port}/${dbConfig.database}`);

    // Comando psql para restore
    const psqlCommand = `psql -h ${dbConfig.host} -p ${dbConfig.port} -U ${dbConfig.user} -d ${dbConfig.database} --no-password < "${backupFile}"`;
    
    // Configurar variável de ambiente para senha
    const env = { ...process.env, PGPASSWORD: dbConfig.password };
    
    console.log('⏳ Executando restore...');
    console.log('💡 Isso pode levar alguns minutos dependendo do tamanho do backup\n');

    try {
      const { stdout, stderr } = await execAsync(psqlCommand, { env });
      
      if (stderr && !stderr.includes('NOTICE') && !stderr.includes('already exists')) {
        console.warn('⚠️  Avisos do psql:', stderr);
      }
      
      console.log('✅ Restore executado!');
      
      // Verificar se o restore funcionou testando uma consulta simples
      console.log('\n📊 Verificando dados restaurados...');
      
      const testCommand = `psql -h ${dbConfig.host} -p ${dbConfig.port} -U ${dbConfig.user} -d ${dbConfig.database} --no-password -c "SELECT COUNT(*) as users FROM users; SELECT COUNT(*) as tournaments FROM tournaments;"`;
      
      try {
        const { stdout: testOutput } = await execAsync(testCommand, { env });
        console.log('✅ Dados verificados:');
        console.log(testOutput);
      } catch (testError) {
        console.warn('⚠️  Não foi possível verificar os dados, mas o restore foi executado');
      }
      
      console.log('🎉 Restore concluído com sucesso!');
      console.log('\n📋 Próximos passos:');
      console.log('1. Gere o cliente Prisma: npm run db:generate');
      console.log('2. Teste a aplicação: npm run dev');
      console.log('3. Verifique os dados: npm run db:studio');
      console.log('4. Execute testes: npm run db:test');
      
    } catch (execError) {
      if (execError.message.includes('psql: command not found')) {
        console.error('❌ psql não encontrado!');
        console.log('\n🔧 Soluções:');
        console.log('1. Instale PostgreSQL client tools');
        console.log('2. Windows: Baixe PostgreSQL do site oficial');
        console.log('3. macOS: brew install postgresql');
        console.log('4. Linux: sudo apt install postgresql-client');
        console.log('\n💡 Ou use o restore JavaScript: node scripts/restore-database.js');
        process.exit(1);
      } else {
        throw execError;
      }
    }

  } catch (error) {
    console.error('\n❌ Erro durante o restore:', error.message);
    console.log('\n🔧 Possíveis soluções:');
    console.log('1. Verifique se o PostgreSQL está rodando');
    console.log('2. Confirme a DATABASE_URL no .env');
    console.log('3. Verifique permissões do usuário do banco');
    console.log('4. Tente criar um novo banco vazio primeiro');
    console.log('5. Use o restore alternativo: node scripts/restore-database.js');
    process.exit(1);
  }
}

// Executar se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  const backupFile = process.argv[2];
  
  if (!backupFile) {
    console.log('📋 Uso: node scripts/pg-restore.js [arquivo-backup.sql]');
    console.log('\n📁 Backups disponíveis:');
    
    const backupDir = 'backups';
    if (fs.existsSync(backupDir)) {
      const files = fs.readdirSync(backupDir)
        .filter(f => f.endsWith('.sql'))
        .sort()
        .reverse(); // Mais recentes primeiro
      
      if (files.length > 0) {
        files.forEach((file, index) => {
          const filePath = path.join(backupDir, file);
          const stats = fs.statSync(filePath);
          const sizeInMB = (stats.size / 1024 / 1024).toFixed(2);
          console.log(`   ${index + 1}. ${file} (${sizeInMB} MB, ${stats.mtime.toLocaleDateString()})`);
        });
        
        console.log(`\n💡 Exemplo: node scripts/pg-restore.js backups/${files[0]}`);
      } else {
        console.log('   Nenhum backup encontrado.');
        console.log('   Execute: node scripts/backup-database.js');
        console.log('   Ou: node scripts/pg-backup.js');
      }
    } else {
      console.log('   Pasta backups não encontrada.');
      console.log('   Execute um backup primeiro.');
    }
    
    process.exit(1);
  }
  
  restorePgBackup(backupFile);
}

export { restorePgBackup };

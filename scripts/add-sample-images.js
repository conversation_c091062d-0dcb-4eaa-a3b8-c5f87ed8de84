import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Imagens de exemplo para torneios
const SAMPLE_TOURNAMENT_IMAGES = [
  {
    banner: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?w=800&h=400&fit=crop&q=80',
    cover: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?w=400&h=300&fit=crop&q=80'
  },
  {
    banner: 'https://images.unsplash.com/photo-1511512578047-dfb367046420?w=800&h=400&fit=crop&q=80',
    cover: 'https://images.unsplash.com/photo-1511512578047-dfb367046420?w=400&h=300&fit=crop&q=80'
  },
  {
    banner: 'https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=800&h=400&fit=crop&q=80',
    cover: 'https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=300&fit=crop&q=80'
  },
  {
    banner: 'https://images.unsplash.com/photo-1560253023-3ec5d502959f?w=800&h=400&fit=crop&q=80',
    cover: 'https://images.unsplash.com/photo-1560253023-3ec5d502959f?w=400&h=300&fit=crop&q=80'
  },
  {
    banner: 'https://images.unsplash.com/photo-1493711662062-fa541adb3fc8?w=800&h=400&fit=crop&q=80',
    cover: 'https://images.unsplash.com/photo-1493711662062-fa541adb3fc8?w=400&h=300&fit=crop&q=80'
  }
];

async function addSampleImages() {
  try {
    console.log('🖼️ Adicionando imagens de exemplo aos torneios...');

    // Buscar todos os torneios sem imagens
    const tournaments = await prisma.tournament.findMany({
      where: {
        OR: [
          { bannerUrl: null },
          { coverImageUrl: null }
        ]
      }
    });

    console.log(`📊 Encontrados ${tournaments.length} torneios para atualizar`);

    for (let i = 0; i < tournaments.length; i++) {
      const tournament = tournaments[i];
      const imageSet = SAMPLE_TOURNAMENT_IMAGES[i % SAMPLE_TOURNAMENT_IMAGES.length];

      await prisma.tournament.update({
        where: { id: tournament.id },
        data: {
          bannerUrl: imageSet.banner,
          coverImageUrl: imageSet.cover
        }
      });

      console.log(`✅ Imagens adicionadas ao torneio: ${tournament.name}`);
    }

    console.log('🎉 Todas as imagens foram adicionadas com sucesso!');

  } catch (error) {
    console.error('❌ Erro ao adicionar imagens:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar o script
addSampleImages();

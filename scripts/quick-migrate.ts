// CAMPMAKER - Script de Migração Rápida para Novo PC
// Execute com: npx tsx scripts/quick-migrate.ts

import fs from 'fs';
import path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

async function quickMigrate() {
  console.log('📦 CAMPMAKER - Migração Rápida para Novo PC\n');
  
  try {
    // 1. Criar backup do banco
    console.log('1️⃣  Criando backup do banco de dados...');
    
    try {
      await execAsync('npx tsx scripts/quick-backup.ts');
      console.log('   ✅ Backup criado com sucesso');
    } catch (error) {
      console.error('   ❌ Erro ao criar backup:', error);
      throw error;
    }

    // 2. Criar pasta de migração
    const migrationDir = 'migration-package';
    if (fs.existsSync(migrationDir)) {
      fs.rmSync(migrationDir, { recursive: true, force: true });
    }
    fs.mkdirSync(migrationDir);
    console.log(`\n2️⃣  Pasta de migração criada: ${migrationDir}`);

    // 3. Copiar backup mais recente
    const backupDir = 'backups';
    const backupFiles = fs.readdirSync(backupDir)
      .filter(f => f.endsWith('.sql'))
      .sort()
      .reverse();
    
    if (backupFiles.length > 0) {
      const latestBackup = path.join(backupDir, backupFiles[0]);
      const migrationBackup = path.join(migrationDir, 'database-backup.sql');
      fs.copyFileSync(latestBackup, migrationBackup);
      console.log(`   ✅ Backup copiado: ${migrationBackup}`);
      
      // Copiar também o JSON
      const jsonFile = latestBackup.replace('.sql', '.json');
      if (fs.existsSync(jsonFile)) {
        const migrationJson = path.join(migrationDir, 'database-backup.json');
        fs.copyFileSync(jsonFile, migrationJson);
        console.log(`   ✅ Backup JSON copiado: ${migrationJson}`);
      }
    }

    // 4. Copiar arquivos essenciais
    console.log('\n3️⃣  Copiando arquivos essenciais...');
    
    const filesToCopy = [
      { src: '.env.example', dest: '.env.example' },
      { src: 'package.json', dest: 'package.json' },
      { src: 'prisma/schema.prisma', dest: 'prisma/schema.prisma' },
      { src: 'README.md', dest: 'README.md' },
      { src: 'scripts/quick-restore.ts', dest: 'scripts/quick-restore.ts' }
    ];

    for (const file of filesToCopy) {
      if (fs.existsSync(file.src)) {
        const destPath = path.join(migrationDir, file.dest);
        const destDir = path.dirname(destPath);
        
        if (!fs.existsSync(destDir)) {
          fs.mkdirSync(destDir, { recursive: true });
        }
        
        fs.copyFileSync(file.src, destPath);
        console.log(`   ✅ ${file.src} → ${file.dest}`);
      }
    }

    // 5. Criar .env para novo PC
    console.log('\n4️⃣  Criando configuração para novo PC...');
    
    const envTemplate = `# CAMPMAKER - Configuração para Novo PC
# Configure estas variáveis antes de executar o projeto

# ================================
# BANCO DE DADOS (PostgreSQL)
# ================================
# IMPORTANTE: Configure sua DATABASE_URL aqui
# Exemplos:
# PostgreSQL local: "postgresql://usuario:senha@localhost:5432/campmaker"
# Supabase: "postgresql://postgres:[SUA-SENHA]@db.[SEU-PROJETO].supabase.co:5432/postgres"
DATABASE_URL="postgresql://postgres:SUA_SENHA@localhost:5432/campmaker"

# ================================
# AUTENTICAÇÃO JWT
# ================================
JWT_SECRET="campmaker_jwt_secret_key_2024_desenvolvimento"

# ================================
# SERVIDOR
# ================================
PORT=5000
NODE_ENV=development
`;

    fs.writeFileSync(path.join(migrationDir, '.env'), envTemplate);
    console.log('   ✅ .env configurado');

    // 6. Criar scripts de setup
    console.log('\n5️⃣  Criando scripts de setup...');
    
    // Script para Windows
    const setupWindows = `@echo off
echo 📦 CAMPMAKER - Setup para Novo PC (Windows)
echo.

echo ⚠️  IMPORTANTE: Configure o arquivo .env antes de continuar!
echo    Edite a DATABASE_URL com suas credenciais do PostgreSQL
echo.
pause

echo 1️⃣  Instalando dependências...
npm install
if %errorlevel% neq 0 (
    echo ❌ Erro ao instalar dependências
    pause
    exit /b 1
)

echo.
echo 2️⃣  Gerando cliente Prisma...
npm run db:generate
if %errorlevel% neq 0 (
    echo ❌ Erro ao gerar cliente Prisma
    echo 💡 Verifique a DATABASE_URL no arquivo .env
    pause
    exit /b 1
)

echo.
echo 3️⃣  Executando migrações...
npm run db:migrate
if %errorlevel% neq 0 (
    echo ❌ Erro nas migrações
    echo 💡 Verifique se o PostgreSQL está rodando
    pause
    exit /b 1
)

echo.
echo 4️⃣  Restaurando backup...
npx tsx scripts/quick-restore.ts database-backup.sql
if %errorlevel% neq 0 (
    echo ❌ Erro ao restaurar backup
    pause
    exit /b 1
)

echo.
echo 🎉 Setup concluído com sucesso!
echo.
echo 📋 Próximos passos:
echo 1. Execute: npm run dev
echo 2. Acesse: http://localhost:3000
echo.
pause
`;

    fs.writeFileSync(path.join(migrationDir, 'setup-windows.bat'), setupWindows);
    console.log('   ✅ setup-windows.bat');

    // Script para Linux/Mac
    const setupUnix = `#!/bin/bash
echo "📦 CAMPMAKER - Setup para Novo PC (Linux/Mac)"
echo ""

echo "⚠️  IMPORTANTE: Configure o arquivo .env antes de continuar!"
echo "   Edite a DATABASE_URL com suas credenciais do PostgreSQL"
echo ""
read -p "Pressione Enter para continuar após configurar o .env..."

echo "1️⃣  Instalando dependências..."
npm install
if [ $? -ne 0 ]; then
    echo "❌ Erro ao instalar dependências"
    exit 1
fi

echo ""
echo "2️⃣  Gerando cliente Prisma..."
npm run db:generate
if [ $? -ne 0 ]; then
    echo "❌ Erro ao gerar cliente Prisma"
    echo "💡 Verifique a DATABASE_URL no arquivo .env"
    exit 1
fi

echo ""
echo "3️⃣  Executando migrações..."
npm run db:migrate
if [ $? -ne 0 ]; then
    echo "❌ Erro nas migrações"
    echo "💡 Verifique se o PostgreSQL está rodando"
    exit 1
fi

echo ""
echo "4️⃣  Restaurando backup..."
npx tsx scripts/quick-restore.ts database-backup.sql
if [ $? -ne 0 ]; then
    echo "❌ Erro ao restaurar backup"
    exit 1
fi

echo ""
echo "🎉 Setup concluído com sucesso!"
echo ""
echo "📋 Próximos passos:"
echo "1. Execute: npm run dev"
echo "2. Acesse: http://localhost:3000"
echo ""
`;

    fs.writeFileSync(path.join(migrationDir, 'setup-unix.sh'), setupUnix);
    fs.chmodSync(path.join(migrationDir, 'setup-unix.sh'), '755');
    console.log('   ✅ setup-unix.sh');

    // 7. Criar README
    const migrationReadme = `# 📦 CAMPMAKER - Pacote de Migração

Este pacote contém tudo que você precisa para configurar o CAMPMAKER em um novo PC.

## 🚀 Como usar

### 1. Configure o banco de dados
Edite o arquivo \`.env\` e configure a \`DATABASE_URL\`:

\`\`\`env
# PostgreSQL local
DATABASE_URL="postgresql://usuario:senha@localhost:5432/campmaker"

# Ou Supabase (mais fácil)
DATABASE_URL="postgresql://postgres:[SUA-SENHA]@db.[PROJETO].supabase.co:5432/postgres"
\`\`\`

### 2. Execute o setup

**Windows:**
\`\`\`bash
setup-windows.bat
\`\`\`

**Linux/Mac:**
\`\`\`bash
chmod +x setup-unix.sh
./setup-unix.sh
\`\`\`

### 3. Teste a aplicação
\`\`\`bash
npm run dev
\`\`\`

Acesse: http://localhost:3000

## 📁 Arquivos incluídos

- ✅ \`database-backup.sql\` - Backup completo do banco
- ✅ \`database-backup.json\` - Backup em formato JSON
- ✅ \`package.json\` - Dependências do projeto
- ✅ \`prisma/schema.prisma\` - Schema do banco
- ✅ Scripts de setup automático

## 🆘 Problemas?

1. **Erro de conexão**: Verifique a DATABASE_URL no .env
2. **Erro nas migrações**: Certifique-se que o PostgreSQL está rodando
3. **Erro no restore**: Verifique se o arquivo database-backup.sql existe

## 💡 Dica

Use Supabase em vez de PostgreSQL local - é mais fácil e não precisa instalar nada!

---
Gerado em: ${new Date().toLocaleString()}
`;

    fs.writeFileSync(path.join(migrationDir, 'README.md'), migrationReadme);
    console.log('   ✅ README.md');

    // 8. Resumo final
    console.log('\n🎉 Pacote de migração criado com sucesso!\n');
    
    const migrationFiles = fs.readdirSync(migrationDir);
    console.log('📁 Arquivos no pacote:');
    migrationFiles.forEach(file => {
      const filePath = path.join(migrationDir, file);
      const stats = fs.statSync(filePath);
      if (stats.isFile()) {
        const size = (stats.size / 1024).toFixed(1);
        console.log(`   📄 ${file} (${size} KB)`);
      } else {
        console.log(`   📁 ${file}/`);
      }
    });

    console.log('\n📋 Próximos passos:');
    console.log('1. 📁 Copie a pasta "migration-package" para o novo PC');
    console.log('2. 🗄️  Configure PostgreSQL (ou use Supabase)');
    console.log('3. ⚙️  Execute o script de setup apropriado');
    console.log('4. 🚀 Teste a aplicação');

    console.log('\n💡 Dicas importantes:');
    console.log('- Use Supabase para facilitar (não precisa instalar PostgreSQL)');
    console.log('- Configure o .env ANTES de executar o setup');
    console.log('- Mantenha este backup seguro');
    console.log('- Teste tudo antes de deletar o projeto original');

  } catch (error: any) {
    console.error('\n❌ Erro ao criar pacote de migração:', error.message);
    process.exit(1);
  }
}

quickMigrate();

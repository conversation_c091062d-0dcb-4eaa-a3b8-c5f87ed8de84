  q3n45m6,7.8/9)_// CAMPMAKER - Script de Restore Rápido
// Execute com: npx tsx scripts/quick-restore.ts [arquivo.sql]

import { prisma } from '../src/lib/prisma.js';
import fs from 'fs';

async function quickRestore(backupFile: string) {
  console.log('🔄 CAMPMAKER - Restore Rápido\n');
  
  try {
    // Verificar se o arquivo existe
    if (!fs.existsSync(backupFile)) {
      console.error(`❌ Arquivo não encontrado: ${backupFile}`);
      console.log('\n📁 Backups disponíveis:');
      
      if (fs.existsSync('backups')) {
        const files = fs.readdirSync('backups')
          .filter(f => f.endsWith('.sql'))
          .sort()
          .reverse();
        
        if (files.length > 0) {
          files.forEach((file, index) => {
            const filePath = `backups/${file}`;
            const stats = fs.statSync(filePath);
            console.log(`   ${index + 1}. ${file} (${(stats.size / 1024).toFixed(1)} KB)`);
          });
          console.log(`\n💡 Exemplo: npx tsx scripts/quick-restore.ts backups/${files[0]}`);
        } else {
          console.log('   Nenhum backup encontrado. Execute: npx tsx scripts/quick-backup.ts');
        }
      }
      process.exit(1);
    }

    const stats = fs.statSync(backupFile);
    console.log(`📁 Arquivo: ${backupFile}`);
    console.log(`📊 Tamanho: ${(stats.size / 1024).toFixed(1)} KB`);
    console.log(`🕒 Modificado: ${stats.mtime.toLocaleString()}\n`);

    // Conectar ao banco
    await prisma.$connect();
    console.log('✅ Conectado ao banco PostgreSQL');

    // Ler arquivo SQL
    const sqlContent = fs.readFileSync(backupFile, 'utf8');
    
    // Dividir em comandos SQL
    const sqlCommands = sqlContent
      .split('\n')
      .filter(line => line.trim() && !line.trim().startsWith('--'))
      .join('\n')
      .split(';')
      .filter(cmd => cmd.trim());

    console.log(`📊 ${sqlCommands.length} comandos SQL encontrados\n`);

    // Executar comandos
    console.log('⏳ Executando restore...');
    
    let successCount = 0;
    let errorCount = 0;

    for (let i = 0; i < sqlCommands.length; i++) {
      const command = sqlCommands[i].trim();
      if (!command) continue;

      try {
        await prisma.$executeRawUnsafe(command);
        successCount++;
        
        // Mostrar progresso
        if ((i + 1) % 20 === 0) {
          console.log(`   📈 ${i + 1}/${sqlCommands.length} comandos processados...`);
        }
      } catch (error: any) {
        errorCount++;
        if (errorCount <= 3) {
          console.warn(`   ⚠️  Aviso no comando ${i + 1}: ${error.message.substring(0, 80)}...`);
        }
        
        // Se houver muitos erros, parar
        if (errorCount > 10) {
          throw new Error('Muitos erros encontrados durante o restore');
        }
      }
    }

    console.log('\n✅ Restore executado!');
    console.log(`   📈 Sucessos: ${successCount}`);
    console.log(`   ⚠️  Avisos: ${errorCount}`);

    // Verificar dados restaurados
    console.log('\n📊 Verificando dados restaurados...');
    
    const userCount = await prisma.user.count();
    const teamCount = await prisma.team.count();
    const tournamentCount = await prisma.tournament.count();
    const registrationCount = await prisma.tournamentRegistration.count();

    console.log(`   👥 ${userCount} usuários`);
    console.log(`   🏆 ${teamCount} times`);
    console.log(`   🎮 ${tournamentCount} torneios`);
    console.log(`   📝 ${registrationCount} inscrições`);

    console.log('\n🎉 Restore concluído com sucesso!');
    console.log('\n📋 Próximos passos:');
    console.log('1. Teste a aplicação: npm run dev');
    console.log('2. Verifique os dados: npm run db:studio');
    console.log('3. Acesse: http://localhost:3000');

  } catch (error: any) {
    console.error('\n❌ Erro durante o restore:', error.message);
    console.log('\n🔧 Possíveis soluções:');
    console.log('1. Verifique se o PostgreSQL está rodando');
    console.log('2. Confirme a DATABASE_URL no .env');
    console.log('3. Execute as migrações: npm run db:migrate');
    console.log('4. Tente com um backup mais recente');
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar se chamado diretamente
const backupFile = process.argv[2];

if (!backupFile) {
  console.log('📋 Uso: npx tsx scripts/quick-restore.ts [arquivo-backup.sql]');
  console.log('\n📁 Backups disponíveis:');
  
  if (fs.existsSync('backups')) {
    const files = fs.readdirSync('backups')
      .filter(f => f.endsWith('.sql'))
      .sort()
      .reverse();
    
    if (files.length > 0) {
      files.forEach((file, index) => {
        const filePath = `backups/${file}`;
        const stats = fs.statSync(filePath);
        console.log(`   ${index + 1}. ${file} (${(stats.size / 1024).toFixed(1)} KB, ${stats.mtime.toLocaleDateString()})`);
      });
      console.log(`\n💡 Exemplo: npx tsx scripts/quick-restore.ts backups/${files[0]}`);
    } else {
      console.log('   Nenhum backup encontrado. Execute: npx tsx scripts/quick-backup.ts');
    }
  } else {
    console.log('   Pasta backups não encontrada. Execute um backup primeiro.');
  }
  
  process.exit(1);
}

quickRestore(backupFile);

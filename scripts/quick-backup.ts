// CAMPMAKER - Script de Backup Rápido e Funcional
// Execute com: npx tsx scripts/quick-backup.ts

import { prisma } from '../src/lib/prisma.js';
import fs from 'fs';

async function quickBackup() {
  console.log('🗄️  CAMPMAKER - Backup Rápido\n');
  
  try {
    // Conectar ao banco
    await prisma.$connect();
    console.log('✅ Conectado ao banco PostgreSQL');

    // Criar pasta de backups
    if (!fs.existsSync('backups')) {
      fs.mkdirSync('backups');
      console.log('📁 Pasta backups criada');
    }

    // Buscar todos os dados
    console.log('\n📊 Coletando dados...');
    
    const users = await prisma.user.findMany({
      include: {
        playerProfile: true
      }
    });
    console.log(`   ✅ ${users.length} usuários`);

    const teams = await prisma.team.findMany({
      include: {
        members: true
      }
    });
    console.log(`   ✅ ${teams.length} times`);

    const tournaments = await prisma.tournament.findMany({
      include: {
        prizeDistributions: true,
        registrations: true
      }
    });
    console.log(`   ✅ ${tournaments.length} torneios`);

    // Nome do arquivo
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const sqlFile = `backups/campmaker-backup-${timestamp}.sql`;
    const jsonFile = `backups/campmaker-backup-${timestamp}.json`;

    // Criar backup JSON (para segurança)
    const jsonBackup = {
      users,
      teams,
      tournaments,
      metadata: {
        timestamp: new Date().toISOString(),
        totalRecords: users.length + teams.length + tournaments.length
      }
    };
    
    fs.writeFileSync(jsonFile, JSON.stringify(jsonBackup, null, 2));
    console.log(`\n💾 Backup JSON: ${jsonFile}`);

    // Criar backup SQL
    let sqlContent = `-- CAMPMAKER - Backup do Banco de Dados
-- Gerado em: ${new Date().toLocaleString()}
-- Total de registros: ${users.length + teams.length + tournaments.length}

-- Limpar dados existentes
DELETE FROM tournament_registrations;
DELETE FROM prize_distributions;
DELETE FROM tournaments;
DELETE FROM team_members;
DELETE FROM player_profiles;
DELETE FROM teams;
DELETE FROM users;

`;

    // Inserir usuários
    if (users.length > 0) {
      sqlContent += '\n-- USUÁRIOS\n';
      for (const user of users) {
        const name = user.name.replace(/'/g, "''");
        const email = user.email.replace(/'/g, "''");
        const password = user.password.replace(/'/g, "''");
        const avatar = user.avatar ? `'${user.avatar.replace(/'/g, "''")}'` : 'NULL';
        
        sqlContent += `INSERT INTO users (id, email, password, name, type, avatar, created_at, updated_at) VALUES ('${user.id}', '${email}', '${password}', '${name}', '${user.type}', ${avatar}, '${user.createdAt.toISOString()}', '${user.updatedAt.toISOString()}');\n`;
      }
    }

    // Inserir times
    if (teams.length > 0) {
      sqlContent += '\n-- TIMES\n';
      for (const team of teams) {
        const name = team.name.replace(/'/g, "''");
        const game = team.game.replace(/'/g, "''");
        const description = team.description ? `'${team.description.replace(/'/g, "''")}'` : 'NULL';
        const logo = team.logo ? `'${team.logo.replace(/'/g, "''")}'` : 'NULL';
        
        sqlContent += `INSERT INTO teams (id, name, game, description, logo, captain_id, created_at) VALUES ('${team.id}', '${name}', '${game}', ${description}, ${logo}, '${team.captainId}', '${team.createdAt.toISOString()}');\n`;
      }
    }

    // Inserir perfis de jogadores
    const playerProfiles = users.filter(u => u.playerProfile).map(u => u.playerProfile);
    if (playerProfiles.length > 0) {
      sqlContent += '\n-- PERFIS DE JOGADORES\n';
      for (const profile of playerProfiles) {
        const peripherals = profile.peripherals ? `'${profile.peripherals.replace(/'/g, "''")}'` : 'NULL';
        const preferredGames = profile.preferredGames.length > 0 ? 
          `ARRAY[${profile.preferredGames.map(g => `'${g.replace(/'/g, "''")}'`).join(', ')}]` : 
          'ARRAY[]::text[]';
        const rank = profile.rank ? `'${profile.rank.replace(/'/g, "''")}'` : 'NULL';
        const twitchUrl = profile.twitchUrl ? `'${profile.twitchUrl.replace(/'/g, "''")}'` : 'NULL';
        const youtubeUrl = profile.youtubeUrl ? `'${profile.youtubeUrl.replace(/'/g, "''")}'` : 'NULL';
        const discordTag = profile.discordTag ? `'${profile.discordTag.replace(/'/g, "''")}'` : 'NULL';
        const teamId = profile.teamId ? `'${profile.teamId}'` : 'NULL';
        
        sqlContent += `INSERT INTO player_profiles (user_id, age, peripherals, preferred_games, rank, twitch_url, youtube_url, discord_tag, team_id, created_at) VALUES ('${profile.userId}', ${profile.age || 'NULL'}, ${peripherals}, ${preferredGames}, ${rank}, ${twitchUrl}, ${youtubeUrl}, ${discordTag}, ${teamId}, '${profile.createdAt.toISOString()}');\n`;
      }
    }

    // Inserir membros de times
    const teamMembers = [];
    for (const team of teams) {
      teamMembers.push(...team.members);
    }
    
    if (teamMembers.length > 0) {
      sqlContent += '\n-- MEMBROS DE TIMES\n';
      for (const member of teamMembers) {
        sqlContent += `INSERT INTO team_members (team_id, player_id, role, joined_at) VALUES ('${member.teamId}', '${member.playerId}', '${member.role}', '${member.joinedAt.toISOString()}');\n`;
      }
    }

    // Inserir torneios
    if (tournaments.length > 0) {
      sqlContent += '\n-- TORNEIOS\n';
      for (const tournament of tournaments) {
        const name = tournament.name.replace(/'/g, "''");
        const game = tournament.game.replace(/'/g, "''");
        const description = tournament.description ? `'${tournament.description.replace(/'/g, "''")}'` : 'NULL';
        const rules = tournament.rules.length > 0 ? 
          `ARRAY[${tournament.rules.map(r => `'${r.replace(/'/g, "''")}'`).join(', ')}]` : 
          'ARRAY[]::text[]';
        const platforms = tournament.platforms.length > 0 ? 
          `ARRAY[${tournament.platforms.map(p => `'${p.replace(/'/g, "''")}'`).join(', ')}]` : 
          'ARRAY[]::text[]';
        
        sqlContent += `INSERT INTO tournaments (id, name, game, organizer_id, description, format, max_participants, registration_fee, prize_pool, rules, platforms, start_date, end_date, registration_deadline, status, created_at, updated_at) VALUES ('${tournament.id}', '${name}', '${game}', '${tournament.organizerId}', ${description}, '${tournament.format}', ${tournament.maxParticipants}, ${tournament.registrationFee}, ${tournament.prizePool}, ${rules}, ${platforms}, '${tournament.startDate.toISOString()}', '${tournament.endDate.toISOString()}', '${tournament.registrationDeadline.toISOString()}', '${tournament.status}', '${tournament.createdAt.toISOString()}', '${tournament.updatedAt.toISOString()}');\n`;
      }
    }

    // Inserir distribuições de prêmios
    const prizeDistributions = [];
    for (const tournament of tournaments) {
      prizeDistributions.push(...tournament.prizeDistributions);
    }
    
    if (prizeDistributions.length > 0) {
      sqlContent += '\n-- DISTRIBUIÇÕES DE PRÊMIOS\n';
      for (const prize of prizeDistributions) {
        sqlContent += `INSERT INTO prize_distributions (id, tournament_id, place, percentage) VALUES ('${prize.id}', '${prize.tournamentId}', ${prize.place}, ${prize.percentage});\n`;
      }
    }

    // Inserir registros de torneios
    const registrations = [];
    for (const tournament of tournaments) {
      registrations.push(...tournament.registrations);
    }
    
    if (registrations.length > 0) {
      sqlContent += '\n-- REGISTROS DE TORNEIOS\n';
      for (const reg of registrations) {
        sqlContent += `INSERT INTO tournament_registrations (id, tournament_id, participant_id, participant_type, registered_at) VALUES ('${reg.id}', '${reg.tournamentId}', '${reg.participantId}', '${reg.participantType}', '${reg.registeredAt.toISOString()}');\n`;
      }
    }

    sqlContent += '\n-- Backup concluído!\n';

    // Salvar arquivo SQL
    fs.writeFileSync(sqlFile, sqlContent);

    // Estatísticas finais
    const sqlStats = fs.statSync(sqlFile);
    const jsonStats = fs.statSync(jsonFile);

    console.log('\n🎉 Backup criado com sucesso!');
    console.log(`\n📁 Arquivos gerados:`);
    console.log(`   📄 SQL: ${sqlFile} (${(sqlStats.size / 1024).toFixed(1)} KB)`);
    console.log(`   📄 JSON: ${jsonFile} (${(jsonStats.size / 1024).toFixed(1)} KB)`);
    
    console.log(`\n📊 Dados salvos:`);
    console.log(`   👥 ${users.length} usuários`);
    console.log(`   🏆 ${teams.length} times`);
    console.log(`   🎮 ${tournaments.length} torneios`);
    console.log(`   📝 ${registrations.length} inscrições`);
    console.log(`   💰 ${prizeDistributions.length} distribuições de prêmios`);

    console.log('\n📋 Para usar em outro PC:');
    console.log('1. Copie os arquivos de backup');
    console.log('2. Configure o projeto no novo PC');
    console.log('3. Execute: npm run db:restore [arquivo-sql]');
    console.log('4. Ou use: psql -d campmaker < [arquivo-sql]');

  } catch (error) {
    console.error('\n❌ Erro durante o backup:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

quickBackup();

// CAMPMAKER - Script de Backup usando pg_dump (PostgreSQL nativo)
// Este script usa pg_dump para criar backups profissionais
// Execute com: node scripts/pg-backup.js

import { exec } from 'child_process';
import fs from 'fs';
import path from 'path';
import { promisify } from 'util';

const execAsync = promisify(exec);

// Função para extrair informações da DATABASE_URL
function parseDatabaseUrl(url) {
  const regex = /postgresql:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/(.+)/;
  const match = url.match(regex);
  
  if (!match) {
    throw new Error('Formato de DATABASE_URL inválido');
  }
  
  return {
    user: match[1],
    password: match[2],
    host: match[3],
    port: match[4],
    database: match[5]
  };
}

async function createPgBackup() {
  try {
    console.log('🗄️  Iniciando backup com pg_dump...\n');

    // Ler DATABASE_URL do .env
    const envContent = fs.readFileSync('.env', 'utf8');
    const databaseUrlMatch = envContent.match(/DATABASE_URL="([^"]+)"/);
    
    if (!databaseUrlMatch) {
      throw new Error('DATABASE_URL não encontrada no arquivo .env');
    }
    
    const databaseUrl = databaseUrlMatch[1];
    const dbConfig = parseDatabaseUrl(databaseUrl);
    
    console.log(`📊 Conectando ao banco: ${dbConfig.host}:${dbConfig.port}/${dbConfig.database}`);

    // Criar pasta de backups se não existir
    const backupDir = 'backups';
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir);
    }

    // Nome do arquivo com timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFile = path.join(backupDir, `campmaker-pgdump-${timestamp}.sql`);

    console.log(`📁 Arquivo de backup: ${backupFile}\n`);

    // Comando pg_dump
    const pgDumpCommand = `pg_dump -h ${dbConfig.host} -p ${dbConfig.port} -U ${dbConfig.user} -d ${dbConfig.database} --no-password --clean --if-exists --create --verbose > "${backupFile}"`;
    
    // Configurar variável de ambiente para senha
    const env = { ...process.env, PGPASSWORD: dbConfig.password };
    
    console.log('⏳ Executando pg_dump...');
    console.log('💡 Isso pode levar alguns minutos dependendo do tamanho do banco\n');

    try {
      const { stdout, stderr } = await execAsync(pgDumpCommand, { env });
      
      if (stderr && !stderr.includes('NOTICE')) {
        console.warn('⚠️  Avisos do pg_dump:', stderr);
      }
      
      // Verificar se o arquivo foi criado
      if (fs.existsSync(backupFile)) {
        const stats = fs.statSync(backupFile);
        
        console.log('🎉 Backup criado com sucesso!');
        console.log(`📁 Arquivo: ${backupFile}`);
        console.log(`📊 Tamanho: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
        console.log(`🕒 Criado em: ${stats.birthtime.toLocaleString()}`);
        
        console.log('\n📋 Para usar em outro PC:');
        console.log('1. Copie o arquivo de backup');
        console.log('2. Configure PostgreSQL no outro PC');
        console.log('3. Execute: node scripts/pg-restore.js [arquivo-backup]');
        console.log('4. Ou use: psql -U usuario -d banco < arquivo-backup.sql');
        
        // Criar script de restore específico para este backup
        const restoreScript = `#!/bin/bash
# Script de restore para ${backupFile}
# Execute com: bash restore-${timestamp}.sh

echo "🔄 Restaurando backup do CAMPMAKER..."
echo "📁 Arquivo: ${backupFile}"
echo ""

# Verificar se o arquivo existe
if [ ! -f "${backupFile}" ]; then
    echo "❌ Arquivo de backup não encontrado!"
    exit 1
fi

# Ler configurações do .env
if [ ! -f ".env" ]; then
    echo "❌ Arquivo .env não encontrado!"
    echo "💡 Copie o .env.example e configure suas variáveis"
    exit 1
fi

# Extrair DATABASE_URL
DATABASE_URL=$(grep "DATABASE_URL=" .env | cut -d'"' -f2)

if [ -z "$DATABASE_URL" ]; then
    echo "❌ DATABASE_URL não encontrada no .env!"
    exit 1
fi

echo "📊 Usando DATABASE_URL do .env"
echo "⏳ Executando restore..."

# Executar restore
psql "$DATABASE_URL" < "${backupFile}"

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 Restore concluído com sucesso!"
    echo "📋 Próximos passos:"
    echo "1. Execute: npm run db:generate"
    echo "2. Teste: npm run dev"
    echo "3. Verifique: npm run db:studio"
else
    echo ""
    echo "❌ Erro durante o restore!"
    echo "🔧 Verifique:"
    echo "1. PostgreSQL está rodando"
    echo "2. DATABASE_URL está correta"
    echo "3. Usuário tem permissões"
fi
`;

        const restoreScriptFile = `restore-${timestamp}.sh`;
        fs.writeFileSync(restoreScriptFile, restoreScript);
        
        console.log(`\n📜 Script de restore criado: ${restoreScriptFile}`);
        
      } else {
        throw new Error('Arquivo de backup não foi criado');
      }
      
    } catch (execError) {
      if (execError.message.includes('pg_dump: command not found')) {
        console.error('❌ pg_dump não encontrado!');
        console.log('\n🔧 Soluções:');
        console.log('1. Instale PostgreSQL client tools');
        console.log('2. Windows: Baixe PostgreSQL do site oficial');
        console.log('3. macOS: brew install postgresql');
        console.log('4. Linux: sudo apt install postgresql-client');
        console.log('\n💡 Ou use o backup JavaScript: node scripts/backup-database.js');
        process.exit(1);
      } else {
        throw execError;
      }
    }

  } catch (error) {
    console.error('❌ Erro ao criar backup:', error.message);
    console.log('\n🔧 Possíveis soluções:');
    console.log('1. Verifique se o PostgreSQL está rodando');
    console.log('2. Confirme a DATABASE_URL no .env');
    console.log('3. Verifique permissões do usuário do banco');
    console.log('4. Use o backup alternativo: node scripts/backup-database.js');
    process.exit(1);
  }
}

// Executar se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  createPgBackup();
}

export { createPgBackup };

// CAMPMAKER - Script de Migração Completa para Novo PC
// Este script facilita todo o processo de migração
// Execute com: node scripts/migrate-to-new-pc.js

import fs from 'fs';
import path from 'path';
import { backupDatabase } from './backup-database.js';
import { createPgBackup } from './pg-backup.js';

async function createMigrationPackage() {
  try {
    console.log('📦 CAMPMAKER - Migração para Novo PC\n');
    console.log('Este script criará um pacote completo para migrar seu projeto\n');

    // Criar pasta de migração
    const migrationDir = 'migration-package';
    if (fs.existsSync(migrationDir)) {
      console.log('🗑️  Removendo pacote anterior...');
      fs.rmSync(migrationDir, { recursive: true, force: true });
    }
    
    fs.mkdirSync(migrationDir);
    console.log(`📁 Pasta criada: ${migrationDir}\n`);

    // 1. Criar backup do banco
    console.log('1️⃣  Criando backup do banco de dados...');
    
    try {
      // Tentar backup com pg_dump primeiro
      await createPgBackup();
      
      // Copiar o backup mais recente para a pasta de migração
      const backupDir = 'backups';
      const backupFiles = fs.readdirSync(backupDir)
        .filter(f => f.includes('pgdump') && f.endsWith('.sql'))
        .sort()
        .reverse();
      
      if (backupFiles.length > 0) {
        const latestBackup = path.join(backupDir, backupFiles[0]);
        const migrationBackup = path.join(migrationDir, 'database-backup.sql');
        fs.copyFileSync(latestBackup, migrationBackup);
        console.log(`   ✅ Backup copiado: ${migrationBackup}`);
      }
      
    } catch (pgError) {
      console.log('   ⚠️  pg_dump falhou, usando backup JavaScript...');
      await backupDatabase();
      
      // Copiar o backup JavaScript mais recente
      const backupDir = 'backups';
      const backupFiles = fs.readdirSync(backupDir)
        .filter(f => !f.includes('pgdump') && f.endsWith('.sql'))
        .sort()
        .reverse();
      
      if (backupFiles.length > 0) {
        const latestBackup = path.join(backupDir, backupFiles[0]);
        const migrationBackup = path.join(migrationDir, 'database-backup.sql');
        fs.copyFileSync(latestBackup, migrationBackup);
        console.log(`   ✅ Backup copiado: ${migrationBackup}`);
      }
    }

    // 2. Copiar arquivos essenciais
    console.log('\n2️⃣  Copiando arquivos essenciais...');
    
    const filesToCopy = [
      { src: '.env.example', dest: '.env.example' },
      { src: 'package.json', dest: 'package.json' },
      { src: 'prisma/schema.prisma', dest: 'prisma/schema.prisma' },
      { src: 'README.md', dest: 'README.md' }
    ];

    for (const file of filesToCopy) {
      if (fs.existsSync(file.src)) {
        const destPath = path.join(migrationDir, file.dest);
        const destDir = path.dirname(destPath);
        
        if (!fs.existsSync(destDir)) {
          fs.mkdirSync(destDir, { recursive: true });
        }
        
        fs.copyFileSync(file.src, destPath);
        console.log(`   ✅ ${file.src} → ${file.dest}`);
      }
    }

    // 3. Criar arquivo .env personalizado (sem senhas)
    console.log('\n3️⃣  Criando arquivo .env para novo PC...');
    
    const envTemplate = `# CAMPMAKER - Configuração para Novo PC
# Configure estas variáveis antes de executar o projeto

# ================================
# BANCO DE DADOS (PostgreSQL)
# ================================
# IMPORTANTE: Configure sua DATABASE_URL aqui
# Exemplos:
# PostgreSQL local: "postgresql://usuario:senha@localhost:5432/campmaker"
# Supabase: "postgresql://postgres:[SUA-SENHA]@db.[SEU-PROJETO].supabase.co:5432/postgres"
DATABASE_URL="postgresql://postgres:SUA_SENHA@localhost:5432/campmaker"

# ================================
# AUTENTICAÇÃO JWT
# ================================
JWT_SECRET="campmaker_jwt_secret_key_2024_desenvolvimento"

# ================================
# SERVIDOR
# ================================
PORT=5000
NODE_ENV=development

# ================================
# INSTRUÇÕES DE CONFIGURAÇÃO
# ================================
# 1. Configure a DATABASE_URL acima com suas credenciais
# 2. Execute: npm install
# 3. Execute: npm run db:generate
# 4. Execute: npm run db:migrate
# 5. Restaure o backup: npm run db:restore migration-package/database-backup.sql
# 6. Teste: npm run dev
`;

    fs.writeFileSync(path.join(migrationDir, '.env'), envTemplate);
    console.log('   ✅ .env configurado para novo PC');

    // 4. Criar scripts de migração
    console.log('\n4️⃣  Criando scripts de migração...');
    
    // Script de setup para Windows
    const setupScriptWindows = `@echo off
echo 📦 CAMPMAKER - Setup para Novo PC (Windows)
echo.

echo 1️⃣  Instalando dependências...
npm install
if %errorlevel% neq 0 (
    echo ❌ Erro ao instalar dependências
    pause
    exit /b 1
)

echo.
echo 2️⃣  Gerando cliente Prisma...
npm run db:generate
if %errorlevel% neq 0 (
    echo ❌ Erro ao gerar cliente Prisma
    echo 💡 Verifique a DATABASE_URL no arquivo .env
    pause
    exit /b 1
)

echo.
echo 3️⃣  Executando migrações...
npm run db:migrate
if %errorlevel% neq 0 (
    echo ❌ Erro nas migrações
    echo 💡 Verifique se o PostgreSQL está rodando e a DATABASE_URL está correta
    pause
    exit /b 1
)

echo.
echo 4️⃣  Restaurando backup do banco...
npm run db:restore database-backup.sql
if %errorlevel% neq 0 (
    echo ❌ Erro ao restaurar backup
    echo 💡 Verifique se o arquivo database-backup.sql existe
    pause
    exit /b 1
)

echo.
echo 🎉 Setup concluído com sucesso!
echo.
echo 📋 Próximos passos:
echo 1. Execute: npm run dev
echo 2. Acesse: http://localhost:3000
echo 3. Verifique os dados: npm run db:studio
echo.
pause
`;

    fs.writeFileSync(path.join(migrationDir, 'setup-windows.bat'), setupScriptWindows);
    console.log('   ✅ setup-windows.bat');

    // Script de setup para Linux/Mac
    const setupScriptUnix = `#!/bin/bash
echo "📦 CAMPMAKER - Setup para Novo PC (Linux/Mac)"
echo ""

echo "1️⃣  Instalando dependências..."
npm install
if [ $? -ne 0 ]; then
    echo "❌ Erro ao instalar dependências"
    exit 1
fi

echo ""
echo "2️⃣  Gerando cliente Prisma..."
npm run db:generate
if [ $? -ne 0 ]; then
    echo "❌ Erro ao gerar cliente Prisma"
    echo "💡 Verifique a DATABASE_URL no arquivo .env"
    exit 1
fi

echo ""
echo "3️⃣  Executando migrações..."
npm run db:migrate
if [ $? -ne 0 ]; then
    echo "❌ Erro nas migrações"
    echo "💡 Verifique se o PostgreSQL está rodando e a DATABASE_URL está correta"
    exit 1
fi

echo ""
echo "4️⃣  Restaurando backup do banco..."
npm run db:restore database-backup.sql
if [ $? -ne 0 ]; then
    echo "❌ Erro ao restaurar backup"
    echo "💡 Verifique se o arquivo database-backup.sql existe"
    exit 1
fi

echo ""
echo "🎉 Setup concluído com sucesso!"
echo ""
echo "📋 Próximos passos:"
echo "1. Execute: npm run dev"
echo "2. Acesse: http://localhost:3000"
echo "3. Verifique os dados: npm run db:studio"
echo ""
`;

    fs.writeFileSync(path.join(migrationDir, 'setup-unix.sh'), setupScriptUnix);
    fs.chmodSync(path.join(migrationDir, 'setup-unix.sh'), '755');
    console.log('   ✅ setup-unix.sh');

    // 5. Criar README de migração
    console.log('\n5️⃣  Criando documentação...');
    
    const migrationReadme = `# 📦 CAMPMAKER - Pacote de Migração

Este pacote contém tudo que você precisa para configurar o projeto CAMPMAKER em um novo PC.

## 📋 O que está incluído

- ✅ Backup completo do banco de dados
- ✅ Arquivos de configuração (package.json, schema.prisma)
- ✅ Scripts de setup automatizado
- ✅ Documentação completa

## 🚀 Como usar

### Opção 1: Setup Automático (Recomendado)

#### Windows:
\`\`\`bash
# 1. Configure o .env com sua DATABASE_URL
# 2. Execute o script de setup
setup-windows.bat
\`\`\`

#### Linux/Mac:
\`\`\`bash
# 1. Configure o .env com sua DATABASE_URL
# 2. Execute o script de setup
chmod +x setup-unix.sh
./setup-unix.sh
\`\`\`

### Opção 2: Setup Manual

1. **Configure o banco de dados**
   \`\`\`bash
   # Edite o arquivo .env e configure a DATABASE_URL
   # Exemplo: "postgresql://usuario:senha@localhost:5432/campmaker"
   \`\`\`

2. **Instale as dependências**
   \`\`\`bash
   npm install
   \`\`\`

3. **Configure o Prisma**
   \`\`\`bash
   npm run db:generate
   npm run db:migrate
   \`\`\`

4. **Restaure o backup**
   \`\`\`bash
   npm run db:restore database-backup.sql
   \`\`\`

5. **Execute o projeto**
   \`\`\`bash
   npm run dev
   \`\`\`

## 🔧 Requisitos

- Node.js 18+ 
- PostgreSQL 12+
- npm ou yarn

## 📊 Verificação

Após o setup, verifique se tudo funcionou:

- **Frontend**: http://localhost:3000
- **Backend**: http://localhost:5000/api/health
- **Banco**: \`npm run db:studio\`

## 🆘 Problemas Comuns

### Erro de conexão com banco
1. Verifique se o PostgreSQL está rodando
2. Confirme a DATABASE_URL no .env
3. Teste a conexão: \`npm run db:test\`

### Erro nas migrações
1. Certifique-se de que o banco existe
2. Verifique permissões do usuário
3. Execute: \`npm run db:push\`

### Erro no restore
1. Verifique se o arquivo database-backup.sql existe
2. Tente: \`npm run db:restore-pg database-backup.sql\`
3. Ou restaure manualmente via psql

## 📞 Suporte

Se encontrar problemas, verifique:
1. README.md principal do projeto
2. Logs de erro detalhados
3. Configurações do PostgreSQL

---

**Data da migração**: ${new Date().toLocaleString()}
**Versão do projeto**: ${JSON.parse(fs.readFileSync('package.json', 'utf8')).version || '1.0.0'}
`;

    fs.writeFileSync(path.join(migrationDir, 'README-MIGRAÇÃO.md'), migrationReadme);
    console.log('   ✅ README-MIGRAÇÃO.md');

    // 6. Resumo final
    console.log('\n🎉 Pacote de migração criado com sucesso!\n');
    
    const migrationFiles = fs.readdirSync(migrationDir);
    console.log('📁 Arquivos no pacote:');
    migrationFiles.forEach(file => {
      const filePath = path.join(migrationDir, file);
      const stats = fs.statSync(filePath);
      const size = stats.isFile() ? ` (${(stats.size / 1024).toFixed(1)} KB)` : '';
      console.log(`   📄 ${file}${size}`);
    });

    console.log('\n📋 Próximos passos:');
    console.log('1. Copie a pasta "migration-package" para o novo PC');
    console.log('2. No novo PC, configure PostgreSQL');
    console.log('3. Execute o script de setup apropriado');
    console.log('4. Teste a aplicação');

    console.log('\n💡 Dicas:');
    console.log('- Use Supabase para facilitar (não precisa instalar PostgreSQL)');
    console.log('- Mantenha o .env seguro (não compartilhe senhas)');
    console.log('- Teste tudo antes de deletar o projeto original');

  } catch (error) {
    console.error('❌ Erro ao criar pacote de migração:', error);
    process.exit(1);
  }
}

// Executar se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  createMigrationPackage();
}

export { createMigrationPackage };

-- CAMPMAKER - Script SQL para criar dados de teste de torneio
-- Execute este script no seu PostgreSQL para criar um torneio completo pronto para testar

-- Limpar dados existentes (opcional)
DELETE FROM tournament_registrations;
DELETE FROM prize_distributions;
DELETE FROM bracket_matches;
DELETE FROM tournaments;
DELETE FROM team_members;
DELETE FROM teams;
DELETE FROM player_profiles;
DELETE FROM users;

-- 1. CRIAR ORGANIZADOR
INSERT INTO users (id, email, password, name, type, company, website, bio, created_at, updated_at) VALUES 
('org-001', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<PERSON> - Organizador', 'organizer', 'E-Sports Brasil', 'https://esportsbrasil.com', 'Organizador profissional de torneios de e-sports', NOW(), NOW());

-- 2. CRIAR 8 TIMES COM CAPITÃES
INSERT INTO users (id, email, password, name, type, created_at, updated_at) VALUES 
('cap-001', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Capitão Furia', 'player', NOW(), NOW()),
('cap-002', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Capitão MIBR', 'player', NOW(), NOW()),
('cap-003', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Capitão Imperial', 'player', NOW(), NOW()),
('cap-004', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Capitão paiN', 'player', NOW(), NOW()),
('cap-005', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Capitão LOUD', 'player', NOW(), NOW()),
('cap-006', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Capitão Fluxo', 'player', NOW(), NOW()),
('cap-007', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Capitão RED Canids', 'player', NOW(), NOW()),
('cap-008', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Capitão Sharks', 'player', NOW(), NOW());

-- 3. CRIAR PERFIS DE JOGADORES PARA OS CAPITÃES
INSERT INTO player_profiles (user_id, preferred_games, rank, discord_tag, created_at) VALUES 
('cap-001', ARRAY['Counter-Strike 2'], 'Global Elite', 'capitao1#1234', NOW()),
('cap-002', ARRAY['Counter-Strike 2'], 'Global Elite', 'capitao2#1234', NOW()),
('cap-003', ARRAY['Counter-Strike 2'], 'Global Elite', 'capitao3#1234', NOW()),
('cap-004', ARRAY['Counter-Strike 2'], 'Global Elite', 'capitao4#1234', NOW()),
('cap-005', ARRAY['Counter-Strike 2'], 'Global Elite', 'capitao5#1234', NOW()),
('cap-006', ARRAY['Counter-Strike 2'], 'Global Elite', 'capitao6#1234', NOW()),
('cap-007', ARRAY['Counter-Strike 2'], 'Global Elite', 'capitao7#1234', NOW()),
('cap-008', ARRAY['Counter-Strike 2'], 'Global Elite', 'capitao8#1234', NOW());

-- 4. CRIAR OS 8 TIMES
INSERT INTO teams (id, name, game, captain_id, created_at, updated_at) VALUES 
('team-001', 'Furia Esports', 'Counter-Strike 2', 'cap-001', NOW(), NOW()),
('team-002', 'MIBR Gaming', 'Counter-Strike 2', 'cap-002', NOW(), NOW()),
('team-003', 'Imperial Esports', 'Counter-Strike 2', 'cap-003', NOW(), NOW()),
('team-004', 'paiN Gaming', 'Counter-Strike 2', 'cap-004', NOW(), NOW()),
('team-005', 'LOUD Esports', 'Counter-Strike 2', 'cap-005', NOW(), NOW()),
('team-006', 'Fluxo Gaming', 'Counter-Strike 2', 'cap-006', NOW(), NOW()),
('team-007', 'RED Canids', 'Counter-Strike 2', 'cap-007', NOW(), NOW()),
('team-008', 'Sharks Esports', 'Counter-Strike 2', 'cap-008', NOW(), NOW());

-- 5. ADICIONAR CAPITÃES COMO MEMBROS DOS TIMES
INSERT INTO team_members (id, team_id, player_id, joined_at) VALUES 
('mem-001', 'team-001', 'cap-001', NOW()),
('mem-002', 'team-002', 'cap-002', NOW()),
('mem-003', 'team-003', 'cap-003', NOW()),
('mem-004', 'team-004', 'cap-004', NOW()),
('mem-005', 'team-005', 'cap-005', NOW()),
('mem-006', 'team-006', 'cap-006', NOW()),
('mem-007', 'team-007', 'cap-007', NOW()),
('mem-008', 'team-008', 'cap-008', NOW());

-- 6. CRIAR TORNEIO
INSERT INTO tournaments (id, name, game, organizer_id, description, format, max_participants, registration_fee, prize_pool, rules, platforms, start_date, end_date, registration_deadline, status, bracket_generated, current_round, total_rounds, created_at, updated_at) VALUES 
('tournament-001', 'Campeonato CS2 - Eliminação Simples', 'Counter-Strike 2', 'org-001', 'Torneio de CS2 com 8 times em formato de eliminação simples. Pronto para iniciar!', 'single-elimination', 8, 0, 5000, ARRAY['Formato: Melhor de 3 (BO3)', 'Mapas: Mirage, Inferno, Dust2, Cache, Overpass', 'Proibido uso de cheats ou exploits', 'Respeito entre jogadores obrigatório'], ARRAY['Steam', 'FACEIT'], '2025-08-10 14:00:00', '2025-08-10 22:00:00', '2025-08-09 23:59:59', 'ready-to-start', false, 1, 3, NOW(), NOW());

-- 7. CONFIGURAR DISTRIBUIÇÃO DE PRÊMIOS
INSERT INTO prize_distributions (id, tournament_id, place, percentage) VALUES 
('prize-001', 'tournament-001', 1, 50.00),
('prize-002', 'tournament-001', 2, 30.00),
('prize-003', 'tournament-001', 3, 20.00);

-- 8. REGISTRAR TODOS OS 8 TIMES NO TORNEIO
INSERT INTO tournament_registrations (id, tournament_id, participant_id, participant_type, registered_at) VALUES 
('reg-001', 'tournament-001', 'team-001', 'team', NOW()),
('reg-002', 'tournament-001', 'team-002', 'team', NOW()),
('reg-003', 'tournament-001', 'team-003', 'team', NOW()),
('reg-004', 'tournament-001', 'team-004', 'team', NOW()),
('reg-005', 'tournament-001', 'team-005', 'team', NOW()),
('reg-006', 'tournament-001', 'team-006', 'team', NOW()),
('reg-007', 'tournament-001', 'team-007', 'team', NOW()),
('reg-008', 'tournament-001', 'team-008', 'team', NOW());

-- 9. VERIFICAR DADOS CRIADOS
SELECT 'RESUMO DOS DADOS CRIADOS:' as info;

SELECT 'Organizador:' as tipo, email, name FROM users WHERE type = 'organizer';

SELECT 'Torneio:' as tipo, name, status, 
       (SELECT COUNT(*) FROM tournament_registrations WHERE tournament_id = tournaments.id) as times_registrados
FROM tournaments;

SELECT 'Times registrados:' as tipo, teams.name 
FROM tournament_registrations tr
JOIN teams ON tr.participant_id = teams.id
ORDER BY teams.name;

-- Mostrar instruções finais
SELECT '
🎉 DADOS DE TESTE CRIADOS COM SUCESSO!

📋 CREDENCIAIS DO ORGANIZADOR:
👤 Email: <EMAIL>
🔑 Senha: 123456

🏆 TORNEIO CRIADO:
📛 Nome: Campeonato CS2 - Eliminação Simples
🎮 Jogo: Counter-Strike 2
📊 Formato: Eliminação Simples
👥 Times: 8 times registrados
💰 Premiação: R$ 5.000
🚦 Status: ready-to-start (Pronto para iniciar!)

🎯 TIMES REGISTRADOS:
1. Furia Esports
2. MIBR Gaming  
3. Imperial Esports
4. paiN Gaming
5. LOUD Esports
6. Fluxo Gaming
7. RED Canids
8. Sharks Esports

🚀 COMO TESTAR:
1. Acesse: http://localhost:3000
2. Faça login como organizador:
   Email: <EMAIL>
   Senha: 123456
3. Vá para a seção de torneios
4. Encontre o torneio "Campeonato CS2 - Eliminação Simples"
5. Clique em "INICIAR TORNEIO" para gerar o bracket
6. Teste a funcionalidade de gerenciamento de partidas

✅ Agora você pode testar o sistema de brackets!
' as instrucoes;

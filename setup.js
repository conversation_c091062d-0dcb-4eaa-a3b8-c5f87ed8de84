#!/usr/bin/env node

/**
 * CAMPMAKER - Script de Setup Automatizado
 * Este script ajuda a configurar o projeto após a instalação do Node.js
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🏆 CAMPMAKER - Setup Automatizado\n');

// Função para executar comandos
function runCommand(command, description) {
  console.log(`📦 ${description}...`);
  try {
    execSync(command, { stdio: 'inherit' });
    console.log(`✅ ${description} - Concluído\n`);
    return true;
  } catch (error) {
    console.error(`❌ Erro em: ${description}`);
    console.error(error.message);
    return false;
  }
}

// Função para verificar se um arquivo existe
function fileExists(filePath) {
  return fs.existsSync(path.join(__dirname, filePath));
}

async function setup() {
  console.log('Verificando pré-requisitos...\n');

  // 1. Verificar Node.js
  try {
    const nodeVersion = execSync('node --version', { encoding: 'utf8' }).trim();
    const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
    console.log(`✅ Node.js: ${nodeVersion}`);
    console.log(`✅ npm: ${npmVersion}\n`);
  } catch (error) {
    console.error('❌ Node.js não encontrado!');
    console.error('Por favor, instale o Node.js: https://nodejs.org/');
    process.exit(1);
  }

  // 2. Verificar arquivo .env
  if (!fileExists('.env')) {
    console.log('❌ Arquivo .env não encontrado!');
    console.log('Por favor, configure o arquivo .env com suas credenciais do banco de dados.');
    process.exit(1);
  }
  console.log('✅ Arquivo .env encontrado\n');

  // 3. Instalar dependências
  if (!runCommand('npm install', 'Instalando dependências')) {
    process.exit(1);
  }

  // 4. Gerar cliente Prisma
  if (!runCommand('npm run db:generate', 'Gerando cliente Prisma')) {
    console.log('⚠️  Erro ao gerar cliente Prisma. Verifique a DATABASE_URL no arquivo .env');
    process.exit(1);
  }

  // 5. Executar migrações
  console.log('🗄️  Configurando banco de dados...');
  console.log('⚠️  Certifique-se de que o PostgreSQL está rodando e a DATABASE_URL está correta');
  
  if (!runCommand('npm run db:migrate', 'Executando migrações do banco')) {
    console.log('❌ Erro nas migrações. Verifique:');
    console.log('   1. PostgreSQL está rodando');
    console.log('   2. DATABASE_URL está correta no .env');
    console.log('   3. Banco de dados existe');
    process.exit(1);
  }

  // 6. Perguntar sobre seed
  console.log('🌱 Deseja popular o banco com dados de teste? (y/n)');
  
  // Para simplificar, vamos executar o seed automaticamente
  console.log('Executando seed automaticamente...');
  runCommand('npm run db:seed', 'Populando banco com dados de teste');

  console.log('🎉 Setup concluído com sucesso!\n');
  console.log('📋 Próximos passos:');
  console.log('   1. Execute: npm run server (em um terminal)');
  console.log('   2. Execute: npm run dev (em outro terminal)');
  console.log('   3. Acesse: http://localhost:3000\n');
  
  console.log('📝 Dados de teste criados:');
  console.log('   Organizador: <EMAIL> / 123456');
  console.log('   Jogador 1: <EMAIL> / 123456');
  console.log('   Jogador 2: <EMAIL> / 123456\n');
  
  console.log('🚀 Divirta-se com o CAMPMAKER!');
}

// Executar setup
setup().catch(error => {
  console.error('❌ Erro durante o setup:', error);
  process.exit(1);
});

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function createSimpleTest() {
  try {
    console.log('🎮 Criando dados simples para teste...\n');

    // Buscar torneio existente
    const tournament = await prisma.tournament.findFirst({
      where: {
        status: 'REGISTRATION'
      },
      orderBy: { createdAt: 'desc' }
    });

    if (!tournament) {
      console.log('❌ Nenhum torneio em período de inscrições encontrado.');
      console.log('💡 Crie um torneio primeiro como organizador.');
      return;
    }

    console.log(`🏆 Torneio encontrado: ${tournament.name}`);

    const hashedPassword = await bcrypt.hash('123456', 10);
    const timestamp = Date.now();

    // Criar apenas 4 times para teste simples
    const teamsData = [
      { playerName: 'Alex Thunder', email: `alex${timestamp}@test.com`, teamName: 'Thunder Bolts' },
      { playerName: 'Bruno Fire', email: `bruno${timestamp}@test.com`, teamName: 'Fire Dragons' },
      { playerName: '<PERSON>', email: `carlos${timestamp}@test.com`, teamName: 'Storm Eagles' },
      { playerName: 'Diego Lightning', email: `diego${timestamp}@test.com`, teamName: 'Lightning Wolves' }
    ];

    for (const teamData of teamsData) {
      console.log(`👤 Criando: ${teamData.playerName} → ${teamData.teamName}`);
      
      // Criar jogador
      const player = await prisma.user.create({
        data: {
          name: teamData.playerName,
          email: teamData.email,
          password: hashedPassword,
          type: 'PLAYER'
        }
      });

      // Criar time
      const team = await prisma.team.create({
        data: {
          name: teamData.teamName,
          game: tournament.game,
          captainId: player.id
        }
      });

      // Adicionar jogador ao time
      await prisma.teamMember.create({
        data: {
          teamId: team.id,
          playerId: player.id
        }
      });

      // Inscrever time no torneio
      await prisma.tournamentRegistration.create({
        data: {
          tournamentId: tournament.id,
          participantId: team.id,
          participantType: 'TEAM'
        }
      });

      console.log(`✅ ${teamData.teamName} criado e inscrito!`);
    }

    console.log('\n🎉 DADOS DE TESTE CRIADOS COM SUCESSO!\n');

    console.log('📊 RESUMO:');
    console.log(`├── 👥 Jogadores criados: ${teamsData.length}`);
    console.log(`├── ⚔️ Times criados: ${teamsData.length}`);
    console.log(`├── 🏆 Torneio: ${tournament.name}`);
    console.log(`└── 📝 Inscrições: ${teamsData.length} times inscritos\n`);

    console.log('🎮 CREDENCIAIS DE LOGIN:');
    teamsData.forEach((team) => {
      console.log(`├── ${team.playerName}: ${team.email} / 123456`);
    });

    console.log('\n🚀 PRÓXIMOS PASSOS:');
    console.log('1. Login como organizador: <EMAIL> / 123456');
    console.log('2. Ir para "Ver Torneios" → Ver Detalhes do torneio');
    console.log('3. Clicar "COMEÇAR TORNEIO" para gerar brackets');
    console.log('4. Ver aba "Competição" para visualizar os confrontos');

  } catch (error) {
    console.error('❌ Erro ao criar dados de teste:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createSimpleTest();

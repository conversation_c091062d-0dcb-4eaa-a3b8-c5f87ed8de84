// Versão do TournamentService que funciona no browser (sem Prisma)
import { Tournament } from '../types';

export interface CreateTournamentData {
  name: string;
  game: string;
  description: string;
  format: 'single-elimination' | 'double-elimination' | 'groups' | 'swiss';
  maxParticipants: number;
  registrationFee: number;
  prizePool: number;
  prizeDistribution: { place: number; percentage: number }[];
  rules: string[];
  platforms: string[];
  startDate: string;
  endDate: string;
  registrationDeadline: string;
}

// Mock data para torneios
const mockTournaments: Tournament[] = [
  {
    id: 'tournament-1',
    name: 'Liga CS2 Brasil 2024',
    game: 'Counter-Strike 2',
    organizerId: 'org-1',
    description: 'Campeonato nacional de CS2 com as melhores equipes do país.',
    format: 'single-elimination',
    maxParticipants: 16,
    registrationFee: 50,
    prizePool: 5000,
    prizeDistribution: [
      { place: 1, percentage: 50 },
      { place: 2, percentage: 30 },
      { place: 3, percentage: 20 }
    ],
    rules: ['Times de 5 jogadores', 'Modo Competitivo', 'Sem trapaça'],
    platforms: ['Steam'],
    startDate: '2024-03-15T10:00:00Z',
    endDate: '2024-03-17T22:00:00Z',
    registrationDeadline: '2024-03-10T23:59:59Z',
    status: 'registration',
    participants: ['player-1'],
    createdAt: '2024-01-15T00:00:00Z'
  },
  {
    id: 'tournament-2',
    name: 'Valorant Cup 2024',
    game: 'Valorant',
    organizerId: 'org-1',
    description: 'Torneio Valorant para jogadores intermediários e avançados.',
    format: 'double-elimination',
    maxParticipants: 32,
    registrationFee: 30,
    prizePool: 3000,
    prizeDistribution: [
      { place: 1, percentage: 60 },
      { place: 2, percentage: 25 },
      { place: 3, percentage: 15 }
    ],
    rules: ['Times de 5 jogadores', 'Modo Padrão', 'Agentes livres'],
    platforms: ['Riot Games'],
    startDate: '2024-04-01T14:00:00Z',
    endDate: '2024-04-03T20:00:00Z',
    registrationDeadline: '2024-03-25T23:59:59Z',
    status: 'upcoming',
    participants: [],
    createdAt: '2024-01-20T00:00:00Z'
  }
];

export class TournamentServiceClient {
  // Listar todos os torneios
  static async getAllTournaments(): Promise<Tournament[]> {
    try {
      // Simular delay de API
      await new Promise(resolve => setTimeout(resolve, 500));
      return [...mockTournaments];
    } catch (error) {
      console.error('Erro ao buscar torneios:', error);
      return [];
    }
  }

  // Criar novo torneio
  static async createTournament(organizerId: string, data: CreateTournamentData): Promise<{ success: boolean; tournament?: Tournament; error?: string }> {
    try {
      // Simular delay de API
      await new Promise(resolve => setTimeout(resolve, 1000));

      const newTournament: Tournament = {
        id: Date.now().toString(),
        name: data.name,
        game: data.game,
        organizerId,
        description: data.description,
        format: data.format,
        maxParticipants: data.maxParticipants,
        registrationFee: data.registrationFee,
        prizePool: data.prizePool,
        prizeDistribution: data.prizeDistribution,
        rules: data.rules.filter(rule => rule.trim() !== ''),
        platforms: data.platforms.filter(platform => platform.trim() !== ''),
        startDate: data.startDate,
        endDate: data.endDate,
        registrationDeadline: data.registrationDeadline,
        status: 'upcoming',
        participants: [],
        createdAt: new Date().toISOString()
      };

      mockTournaments.unshift(newTournament);

      return {
        success: true,
        tournament: newTournament
      };

    } catch (error) {
      console.error('Erro ao criar torneio:', error);
      return {
        success: false,
        error: 'Erro ao criar torneio'
      };
    }
  }

  // Registrar participante em torneio
  static async registerForTournament(tournamentId: string, participantId: string, participantType: 'player' | 'team' = 'player'): Promise<{ success: boolean; error?: string }> {
    try {
      // Simular delay de API
      await new Promise(resolve => setTimeout(resolve, 500));

      const tournamentIndex = mockTournaments.findIndex(t => t.id === tournamentId);
      if (tournamentIndex === -1) {
        return { success: false, error: 'Torneio não encontrado' };
      }

      const tournament = mockTournaments[tournamentIndex];

      if (tournament.status !== 'registration') {
        return { success: false, error: 'Torneio não está aberto para inscrições' };
      }

      if (tournament.participants.length >= tournament.maxParticipants) {
        return { success: false, error: 'Torneio lotado' };
      }

      if (tournament.participants.includes(participantId)) {
        return { success: false, error: 'Já inscrito neste torneio' };
      }

      // Adicionar participante
      mockTournaments[tournamentIndex].participants.push(participantId);

      return { success: true };

    } catch (error) {
      console.error('Erro ao registrar no torneio:', error);
      return { success: false, error: 'Erro ao registrar no torneio' };
    }
  }

  // Buscar torneios do organizador
  static async getOrganizerTournaments(organizerId: string): Promise<Tournament[]> {
    try {
      await new Promise(resolve => setTimeout(resolve, 300));
      return mockTournaments.filter(t => t.organizerId === organizerId);
    } catch (error) {
      console.error('Erro ao buscar torneios do organizador:', error);
      return [];
    }
  }

  // Buscar torneios do jogador
  static async getPlayerTournaments(playerId: string): Promise<Tournament[]> {
    try {
      await new Promise(resolve => setTimeout(resolve, 300));
      return mockTournaments.filter(t => t.participants.includes(playerId));
    } catch (error) {
      console.error('Erro ao buscar torneios do jogador:', error);
      return [];
    }
  }
}

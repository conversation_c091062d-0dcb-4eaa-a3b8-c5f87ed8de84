import { prisma } from '../lib/prisma';
import { Tournament, BracketMatch } from '../types';

export interface CreateTournamentData {
  name: string;
  game: string;
  description: string;
  format: 'single-elimination' | 'double-elimination' | 'groups' | 'swiss';
  maxParticipants: number;
  registrationFee: number;
  prizePool: number;
  prizeDistribution: { place: number; percentage: number }[];
  rules: string[];
  platforms: string[];
  startDate: string;
  endDate: string;
  registrationDeadline: string;
}

export class TournamentService {
  // Listar todos os torneios
  static async getAllTournaments(): Promise<Tournament[]> {
    try {
      const tournaments = await prisma.tournament.findMany({
        include: {
          organizer: {
            select: { id: true, name: true, email: true }
          },
          prizeDistributions: true,
          registrations: true,
          _count: {
            select: { registrations: true }
          }
        },
        orderBy: { createdAt: 'desc' }
      });

      return tournaments.map(tournament => ({
        id: tournament.id,
        name: tournament.name,
        game: tournament.game,
        organizerId: tournament.organizerId,
        description: tournament.description || '',
        format: tournament.format.toLowerCase().replace('_', '-') as any,
        maxParticipants: tournament.maxParticipants,
        registrationFee: Number(tournament.registrationFee),
        prizePool: Number(tournament.prizePool),
        prizeDistribution: tournament.prizeDistributions.map(pd => ({
          place: pd.place,
          percentage: Number(pd.percentage)
        })),
        rules: tournament.rules,
        platforms: tournament.platforms,
        startDate: tournament.startDate.toISOString(),
        endDate: tournament.endDate.toISOString(),
        registrationDeadline: tournament.registrationDeadline.toISOString(),
        status: tournament.status.toLowerCase() as any,
        participants: tournament.registrations.map(r => r.participantId),
        createdAt: tournament.createdAt.toISOString()
      }));

    } catch (error) {
      console.error('Erro ao buscar torneios:', error);
      return [];
    }
  }

  // Criar novo torneio
  static async createTournament(organizerId: string, data: CreateTournamentData): Promise<{ success: boolean; tournament?: Tournament; error?: string }> {
    try {
      const tournament = await prisma.tournament.create({
        data: {
          name: data.name,
          game: data.game,
          organizerId,
          description: data.description,
          format: data.format.toUpperCase().replace('-', '_') as any,
          maxParticipants: data.maxParticipants,
          registrationFee: data.registrationFee,
          prizePool: data.prizePool,
          rules: data.rules.filter(rule => rule.trim() !== ''),
          platforms: data.platforms.filter(platform => platform.trim() !== ''),
          startDate: new Date(data.startDate),
          endDate: new Date(data.endDate),
          registrationDeadline: new Date(data.registrationDeadline),
          status: 'UPCOMING',
          prizeDistributions: {
            create: data.prizeDistribution.map(pd => ({
              place: pd.place,
              percentage: pd.percentage
            }))
          }
        },
        include: {
          organizer: {
            select: { id: true, name: true, email: true }
          },
          prizeDistributions: true,
          registrations: true
        }
      });

      const tournamentResponse: Tournament = {
        id: tournament.id,
        name: tournament.name,
        game: tournament.game,
        organizerId: tournament.organizerId,
        description: tournament.description || '',
        format: tournament.format.toLowerCase().replace('_', '-') as any,
        maxParticipants: tournament.maxParticipants,
        registrationFee: Number(tournament.registrationFee),
        prizePool: Number(tournament.prizePool),
        prizeDistribution: tournament.prizeDistributions.map(pd => ({
          place: pd.place,
          percentage: Number(pd.percentage)
        })),
        rules: tournament.rules,
        platforms: tournament.platforms,
        startDate: tournament.startDate.toISOString(),
        endDate: tournament.endDate.toISOString(),
        registrationDeadline: tournament.registrationDeadline.toISOString(),
        status: tournament.status.toLowerCase() as any,
        participants: [],
        createdAt: tournament.createdAt.toISOString()
      };

      return {
        success: true,
        tournament: tournamentResponse
      };

    } catch (error) {
      console.error('Erro ao criar torneio:', error);
      return {
        success: false,
        error: 'Erro ao criar torneio'
      };
    }
  }

  // Registrar participante em torneio
  static async registerForTournament(tournamentId: string, participantId: string, participantType: 'player' | 'team' = 'player'): Promise<{ success: boolean; error?: string }> {
    try {
      // Verificar se o torneio existe e está aberto para inscrições
      const tournament = await prisma.tournament.findUnique({
        where: { id: tournamentId },
        include: { registrations: true }
      });

      if (!tournament) {
        return { success: false, error: 'Torneio não encontrado' };
      }

      if (tournament.status !== 'REGISTRATION') {
        return { success: false, error: 'Torneio não está aberto para inscrições' };
      }

      if (tournament.registrations.length >= tournament.maxParticipants) {
        return { success: false, error: 'Torneio lotado' };
      }

      // Verificar se já está inscrito
      const existingRegistration = await prisma.tournamentRegistration.findUnique({
        where: {
          tournamentId_participantId: {
            tournamentId,
            participantId
          }
        }
      });

      if (existingRegistration) {
        return { success: false, error: 'Já inscrito neste torneio' };
      }

      // Criar inscrição
      await prisma.tournamentRegistration.create({
        data: {
          tournamentId,
          participantId,
          participantType: participantType.toUpperCase() as any
        }
      });

      return { success: true };

    } catch (error) {
      console.error('Erro ao registrar no torneio:', error);
      return { success: false, error: 'Erro ao registrar no torneio' };
    }
  }

  // Buscar torneios do organizador
  static async getOrganizerTournaments(organizerId: string): Promise<Tournament[]> {
    try {
      const tournaments = await prisma.tournament.findMany({
        where: { organizerId },
        include: {
          organizer: {
            select: { id: true, name: true, email: true }
          },
          prizeDistributions: true,
          registrations: true
        },
        orderBy: { createdAt: 'desc' }
      });

      return tournaments.map(tournament => ({
        id: tournament.id,
        name: tournament.name,
        game: tournament.game,
        organizerId: tournament.organizerId,
        description: tournament.description || '',
        format: tournament.format.toLowerCase().replace('_', '-') as any,
        maxParticipants: tournament.maxParticipants,
        registrationFee: Number(tournament.registrationFee),
        prizePool: Number(tournament.prizePool),
        prizeDistribution: tournament.prizeDistributions.map(pd => ({
          place: pd.place,
          percentage: Number(pd.percentage)
        })),
        rules: tournament.rules,
        platforms: tournament.platforms,
        startDate: tournament.startDate.toISOString(),
        endDate: tournament.endDate.toISOString(),
        registrationDeadline: tournament.registrationDeadline.toISOString(),
        status: tournament.status.toLowerCase() as any,
        participants: tournament.registrations.map(r => r.participantId),
        createdAt: tournament.createdAt.toISOString()
      }));

    } catch (error) {
      console.error('Erro ao buscar torneios do organizador:', error);
      return [];
    }
  }

  // Buscar torneios do jogador
  static async getPlayerTournaments(playerId: string): Promise<Tournament[]> {
    try {
      const registrations = await prisma.tournamentRegistration.findMany({
        where: { participantId: playerId },
        include: {
          tournament: {
            include: {
              organizer: {
                select: { id: true, name: true, email: true }
              },
              prizeDistributions: true,
              registrations: true
            }
          }
        }
      });

      return registrations.map(reg => {
        const tournament = reg.tournament;
        return {
          id: tournament.id,
          name: tournament.name,
          game: tournament.game,
          organizerId: tournament.organizerId,
          description: tournament.description || '',
          format: tournament.format.toLowerCase().replace('_', '-') as any,
          maxParticipants: tournament.maxParticipants,
          registrationFee: Number(tournament.registrationFee),
          prizePool: Number(tournament.prizePool),
          prizeDistribution: tournament.prizeDistributions.map(pd => ({
            place: pd.place,
            percentage: Number(pd.percentage)
          })),
          rules: tournament.rules,
          platforms: tournament.platforms,
          startDate: tournament.startDate.toISOString(),
          endDate: tournament.endDate.toISOString(),
          registrationDeadline: tournament.registrationDeadline.toISOString(),
          status: tournament.status.toLowerCase() as any,
          participants: tournament.registrations.map(r => r.participantId),
          createdAt: tournament.createdAt.toISOString()
        };
      });

    } catch (error) {
      console.error('Erro ao buscar torneios do jogador:', error);
      return [];
    }
  }
}

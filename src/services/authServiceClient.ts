// Versão do AuthService que funciona no browser (sem Prisma)
import { User, UserType } from '../types';

export interface AuthResponse {
  success: boolean;
  user?: User;
  token?: string;
  error?: string;
}

export interface RegisterData {
  email: string;
  password: string;
  name: string;
  type: UserType;
}

export interface LoginData {
  email: string;
  password: string;
  type: UserType;
}

// Função simples para criar token
function createSimpleToken(userId: string, email: string, type: string): string {
  const payload = { userId, email, type, exp: Date.now() + (7 * 24 * 60 * 60 * 1000) }; // 7 dias
  return btoa(JSON.stringify(payload));
}

// Função para verificar token simples
function verifySimpleToken(token: string): any {
  try {
    const payload = JSON.parse(atob(token));
    if (payload.exp < Date.now()) {
      throw new Error('Token expirado');
    }
    return payload;
  } catch {
    throw new Error('Token inválido');
  }
}

// Simulação de API calls (por enquanto usando dados mock)
const mockUsers = [
  {
    id: 'org-1',
    email: '<EMAIL>',
    password: '123456', // Em produção seria hash
    name: 'João Organizador',
    type: 'ORGANIZER'
  },
  {
    id: 'player-1',
    email: '<EMAIL>',
    password: '123456',
    name: 'Maria Jogadora',
    type: 'PLAYER'
  }
];

export class AuthServiceClient {
  // Registrar novo usuário
  static async register(data: RegisterData): Promise<AuthResponse> {
    try {
      // Simular delay de API
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Verificar se email já existe
      const existingUser = mockUsers.find(u => u.email === data.email);
      if (existingUser) {
        return {
          success: false,
          error: 'Email já está em uso'
        };
      }

      // Criar novo usuário
      const newUser = {
        id: Date.now().toString(),
        email: data.email,
        password: data.password,
        name: data.name,
        type: data.type === 'player' ? 'PLAYER' : 'ORGANIZER'
      };

      mockUsers.push(newUser);

      // Gerar token
      const token = createSimpleToken(newUser.id, newUser.email, newUser.type);

      const user: User = {
        id: newUser.id,
        email: newUser.email,
        name: newUser.name,
        type: newUser.type.toLowerCase() as UserType,
        createdAt: new Date().toISOString()
      };

      return {
        success: true,
        user,
        token
      };

    } catch (error) {
      return {
        success: false,
        error: 'Erro interno do servidor'
      };
    }
  }

  // Login do usuário
  static async login(data: LoginData): Promise<AuthResponse> {
    try {
      // Simular delay de API
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Buscar usuário
      const user = mockUsers.find(u => 
        u.email === data.email && 
        u.type === (data.type === 'player' ? 'PLAYER' : 'ORGANIZER')
      );

      if (!user || user.password !== data.password) {
        return {
          success: false,
          error: 'Credenciais inválidas'
        };
      }

      // Gerar token
      const token = createSimpleToken(user.id, user.email, user.type);

      const userResponse: User = {
        id: user.id,
        email: user.email,
        name: user.name,
        type: user.type.toLowerCase() as UserType,
        createdAt: new Date().toISOString()
      };

      return {
        success: true,
        user: userResponse,
        token
      };

    } catch (error) {
      return {
        success: false,
        error: 'Erro interno do servidor'
      };
    }
  }

  // Verificar token
  static async verifyToken(token: string): Promise<AuthResponse> {
    try {
      const decoded = verifySimpleToken(token);
      
      const user = mockUsers.find(u => u.id === decoded.userId);
      if (!user) {
        return {
          success: false,
          error: 'Token inválido'
        };
      }

      const userResponse: User = {
        id: user.id,
        email: user.email,
        name: user.name,
        type: user.type.toLowerCase() as UserType,
        createdAt: new Date().toISOString()
      };

      return {
        success: true,
        user: userResponse,
        token
      };

    } catch (error) {
      return {
        success: false,
        error: 'Token inválido'
      };
    }
  }

  // Atualizar perfil
  static async updateProfile(userId: string, updates: Partial<User>): Promise<AuthResponse> {
    try {
      const userIndex = mockUsers.findIndex(u => u.id === userId);
      if (userIndex === -1) {
        return {
          success: false,
          error: 'Usuário não encontrado'
        };
      }

      // Atualizar dados
      if (updates.name) mockUsers[userIndex].name = updates.name;

      const user = mockUsers[userIndex];
      const userResponse: User = {
        id: user.id,
        email: user.email,
        name: user.name,
        type: user.type.toLowerCase() as UserType,
        createdAt: new Date().toISOString()
      };

      return {
        success: true,
        user: userResponse
      };

    } catch (error) {
      return {
        success: false,
        error: 'Erro ao atualizar perfil'
      };
    }
  }
}

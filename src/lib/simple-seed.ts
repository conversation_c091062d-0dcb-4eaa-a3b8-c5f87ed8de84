import { prisma } from './prisma';
import bcrypt from 'bcryptjs';

async function main() {
  console.log('🌱 Iniciando seed simples...');

  try {
    // Limpar dados existentes primeiro
    await prisma.tournamentRegistration.deleteMany();
    await prisma.prizeDistribution.deleteMany();
    await prisma.tournament.deleteMany();
    await prisma.playerProfile.deleteMany();
    await prisma.user.deleteMany();

    // Criar organizador
    const organizer = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: await bcrypt.hash('123456', 10),
        name: '<PERSON>',
        type: 'ORGANIZER'
      }
    });
    console.log('✅ Organizador criado:', organizer.name);

    // Criar jogador
    const player = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: await bcrypt.hash('123456', 10),
        name: '<PERSON>',
        type: '<PERSON><PERSON>Y<PERSON>',
        playerProfile: {
          create: {
            preferredGames: ['Counter-Strike 2', 'Valorant'],
            age: 22,
            rank: 'Global Elite'
          }
        }
      }
    });
    console.log('✅ Jogador criado:', player.name);

    // Criar mais jogadores
    const player2 = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: await bcrypt.hash('123456', 10),
        name: 'Pedro Costa',
        type: 'PLAYER',
        playerProfile: {
          create: {
            preferredGames: ['Counter-Strike 2', 'League of Legends'],
            age: 25,
            rank: 'Supreme Master'
          }
        }
      }
    });

    const player3 = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: await bcrypt.hash('123456', 10),
        name: 'Ana Oliveira',
        type: 'PLAYER',
        playerProfile: {
          create: {
            preferredGames: ['Valorant', 'Apex Legends'],
            age: 20,
            rank: 'Immortal'
          }
        }
      }
    });

    // Criar times por jogo
    const teamCS2 = await prisma.team.create({
      data: {
        name: 'Thunder Wolves CS2',
        game: 'Counter-Strike 2',
        captainId: player.id,
        members: {
          create: [
            { playerId: player.id },
            { playerId: player2.id }
          ]
        }
      }
    });
    console.log('✅ Time CS2 criado:', teamCS2.name);

    const teamValorant = await prisma.team.create({
      data: {
        name: 'Fire Dragons Valorant',
        game: 'Valorant',
        captainId: player.id, // Mesmo jogador pode ser capitão de times de jogos diferentes
        members: {
          create: [
            { playerId: player.id },
            { playerId: player3.id }
          ]
        }
      }
    });
    console.log('✅ Time Valorant criado:', teamValorant.name);

    // Criar torneios
    const tournament1 = await prisma.tournament.create({
      data: {
        name: 'Liga CS2 Brasil 2024',
        game: 'Counter-Strike 2',
        organizerId: organizer.id,
        description: 'Campeonato nacional de CS2 com as melhores equipes do país.',
        format: 'SINGLE_ELIMINATION',
        maxParticipants: 16,
        registrationFee: 50,
        prizePool: 5000,
        rules: ['Times de 5 jogadores', 'Modo Competitivo', 'Sem trapaça'],
        platforms: ['Steam'],
        startDate: new Date('2024-03-15T10:00:00Z'),
        endDate: new Date('2024-03-17T22:00:00Z'),
        registrationDeadline: new Date('2024-03-10T23:59:59Z'),
        status: 'REGISTRATION',
        prizeDistributions: {
          create: [
            { place: 1, percentage: 50 },
            { place: 2, percentage: 30 },
            { place: 3, percentage: 20 }
          ]
        }
      }
    });

    const tournament2 = await prisma.tournament.create({
      data: {
        name: 'Valorant Cup 2024',
        game: 'Valorant',
        organizerId: organizer.id,
        description: 'Torneio Valorant para jogadores intermediários e avançados.',
        format: 'DOUBLE_ELIMINATION',
        maxParticipants: 32,
        registrationFee: 30,
        prizePool: 3000,
        rules: ['Times de 5 jogadores', 'Modo Padrão', 'Agentes livres'],
        platforms: ['Riot Games'],
        startDate: new Date('2024-04-01T14:00:00Z'),
        endDate: new Date('2024-04-03T20:00:00Z'),
        registrationDeadline: new Date('2024-03-25T23:59:59Z'),
        status: 'UPCOMING',
        prizeDistributions: {
          create: [
            { place: 1, percentage: 60 },
            { place: 2, percentage: 25 },
            { place: 3, percentage: 15 }
          ]
        }
      }
    });
    console.log('✅ Torneios criados:', tournament1.name, 'e', tournament2.name);

    // Registrar jogador no torneio (comentado por enquanto devido ao erro de FK)
    // await prisma.tournamentRegistration.create({
    //   data: {
    //     tournamentId: tournament.id,
    //     participantId: player.id,
    //     participantType: 'PLAYER'
    //   }
    // });
    // console.log('✅ Jogador registrado no torneio');

    console.log('🎉 Seed concluído!');

  } catch (error) {
    console.error('❌ Erro no seed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();

import { prisma } from './prisma';
import bcrypt from 'bcryptjs';

export async function testDatabase() {
  try {
    console.log('🧪 Testando conexão com o banco...');
    
    // Teste 1: Conectar ao banco
    await prisma.$connect();
    console.log('✅ Conexão estabelecida');

    // Teste 2: Criar usuário de teste
    const hashedPassword = await bcrypt.hash('123456', 10);
    
    const testUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: '<PERSON>u<PERSON><PERSON> Teste',
        type: 'PLAYER'
      }
    });
    console.log('✅ Usuário criado:', testUser.name);

    // Teste 3: Buscar usuário
    const foundUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });
    console.log('✅ Usuário encontrado:', foundUser?.name);

    // Teste 4: <PERSON><PERSON><PERSON> tornei<PERSON> de teste
    const testTournament = await prisma.tournament.create({
      data: {
        name: 'Torneio Teste',
        game: 'Counter-Strike 2',
        organizerId: testUser.id,
        description: 'Torneio para testar o sistema',
        format: 'SINGLE_ELIMINATION',
        maxParticipants: 16,
        registrationFee: 50,
        prizePool: 1000,
        rules: ['Sem trapaça', 'Respeito aos adversários'],
        platforms: ['Steam'],
        startDate: new Date('2024-03-01'),
        endDate: new Date('2024-03-03'),
        registrationDeadline: new Date('2024-02-25'),
        status: 'REGISTRATION'
      }
    });
    console.log('✅ Torneio criado:', testTournament.name);

    // Teste 5: Listar todos os usuários
    const allUsers = await prisma.user.findMany();
    console.log('✅ Total de usuários:', allUsers.length);

    // Teste 6: Listar todos os torneios
    const allTournaments = await prisma.tournament.findMany({
      include: {
        organizer: {
          select: { name: true, email: true }
        }
      }
    });
    console.log('✅ Total de torneios:', allTournaments.length);

    console.log('🎉 Todos os testes passaram! Banco funcionando perfeitamente!');
    
    return {
      success: true,
      users: allUsers.length,
      tournaments: allTournaments.length
    };

  } catch (error) {
    console.error('❌ Erro nos testes:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  } finally {
    await prisma.$disconnect();
  }
}

// Função para limpar dados de teste
export async function cleanTestData() {
  try {
    await prisma.tournament.deleteMany({
      where: { name: 'Torneio Teste' }
    });

    await prisma.user.deleteMany({
      where: { email: '<EMAIL>' }
    });

    console.log('✅ Dados de teste removidos');
  } catch (error) {
    console.error('❌ Erro ao limpar dados:', error);
  }
}

// Executar teste se este arquivo for chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  console.log('🚀 Iniciando testes do banco de dados...\n');

  testDatabase().then(result => {
    if (result.success) {
      console.log('\n🎉 SUCESSO! Banco de dados funcionando perfeitamente!');
      console.log(`📊 Estatísticas:`);
      console.log(`   - Usuários: ${result.users}`);
      console.log(`   - Torneios: ${result.tournaments}`);

      // Limpar dados de teste
      console.log('\n🧹 Limpando dados de teste...');
      cleanTestData().then(() => {
        process.exit(0);
      });

    } else {
      console.log('\n❌ ERRO! Problemas encontrados:');
      console.log(`   ${result.error}`);
      process.exit(1);
    }
  }).catch(console.error);
}

import React from 'react';
import { Trophy, Users } from 'lucide-react';

interface GameCardProps {
  game: string;
  tournamentCount: number;
  activeCount: number;
  imageUrl: string;
  onClick: () => void;
}

export function GameCard({ game, tournamentCount, activeCount, imageUrl, onClick }: GameCardProps) {
  return (
    <div
      onClick={onClick}
      className="group relative bg-gray-800 rounded-xl overflow-hidden border border-gray-700 hover:border-purple-500/50 transition-all duration-300 cursor-pointer hover:scale-[1.02] hover:shadow-xl"
    >
      {/* Game Image */}
      <div className="relative h-48 overflow-hidden">
        <img
          src={imageUrl}
          alt={game}
          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />
        
        {/* Active tournaments badge */}
        {activeCount > 0 && (
          <div className="absolute top-3 right-3 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1">
            <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
            <span>{activeCount} ativos</span>
          </div>
        )}
      </div>

      {/* Game Info */}
      <div className="p-6">
        <h3 className="text-xl font-bold text-white mb-2 group-hover:text-purple-400 transition-colors">
          {game}
        </h3>
        
        <div className="flex items-center justify-between text-sm text-gray-400">
          <div className="flex items-center space-x-1">
            <Trophy className="w-4 h-4" />
            <span>{tournamentCount} torneios</span>
          </div>
          
          <div className="flex items-center space-x-1">
            <Users className="w-4 h-4" />
            <span>Ver todos</span>
          </div>
        </div>
      </div>

      {/* Hover overlay */}
      <div className="absolute inset-0 bg-purple-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
    </div>
  );
}

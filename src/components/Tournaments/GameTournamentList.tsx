import React, { useState, useEffect } from 'react';
import { 
  Search, 
  ArrowLeft, 
  Filter,
  DollarSign,
  Calendar,
  Users,
  Trophy
} from 'lucide-react';
import { apiService } from '../../services/apiService';
import { Tournament } from '../../types';

interface GameTournamentListProps {
  game: string;
  onBack: () => void;
  onTournamentDetails: (tournamentId: string) => void;
}

export function GameTournamentList({ game, onBack, onTournamentDetails }: GameTournamentListProps) {
  const [tournaments, setTournaments] = useState<Tournament[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [feeRange, setFeeRange] = useState([0, 100]);
  const [sortBy, setSortBy] = useState('startDate');

  const statuses = [
    { value: 'all', label: 'Todos os Status' },
    { value: 'registration', label: 'Inscrições Abertas' },
    { value: 'upcoming', label: 'Próximos' },
    { value: 'ready-to-start', label: 'Prontos para Iniciar' },
    { value: 'ongoing', label: 'Em Andamento' },
    { value: 'completed', label: 'Finalizados' }
  ];

  useEffect(() => {
    loadTournaments();
  }, [game]);

  const loadTournaments = async () => {
    try {
      setLoading(true);
      const response = await apiService.getTournaments();
      
      if (response.success && response.tournaments) {
        // Filtrar apenas torneios do jogo selecionado
        const gameTournaments = response.tournaments.filter(t => t.game === game);
        setTournaments(gameTournaments);
      }
    } catch (error) {
      console.error('Erro ao carregar torneios:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredTournaments = tournaments
    .filter(tournament => {
      const matchesSearch = tournament.name.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = selectedStatus === 'all' || tournament.status === selectedStatus;
      const matchesFee = tournament.registrationFee >= feeRange[0] && tournament.registrationFee <= feeRange[1];
      
      return matchesSearch && matchesStatus && matchesFee;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'prizePool':
          return b.prizePool - a.prizePool;
        case 'participants':
          return b.participants.length - a.participants.length;
        case 'registrationFee':
          return a.registrationFee - b.registrationFee;
        case 'startDate':
        default:
          return new Date(a.startDate).getTime() - new Date(b.startDate).getTime();
      }
    });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'registration':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'upcoming':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'ready-to-start':
        return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'ongoing':
        return 'bg-purple-500/20 text-purple-400 border-purple-500/30';
      case 'completed':
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'registration':
        return 'Inscrições Abertas';
      case 'upcoming':
        return 'Próximo';
      case 'ready-to-start':
        return 'Pronto para Iniciar';
      case 'ongoing':
        return 'Em Andamento';
      case 'completed':
        return 'Finalizado';
      default:
        return status;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="text-center py-16">
          <div className="w-8 h-8 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-400">Carregando torneios de {game}...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <button
          onClick={onBack}
          className="p-2 text-gray-400 hover:text-white transition-colors"
        >
          <ArrowLeft className="w-5 h-5" />
        </button>
        <div>
          <h1 className="text-3xl font-bold text-white">{game}</h1>
          <p className="text-gray-400 mt-1">{filteredTournaments.length} torneios encontrados</p>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
        <div className="space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder="Buscar torneios..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 bg-gray-900 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>

          {/* Filters Row */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Status Filter */}
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="px-4 py-3 bg-gray-900 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            >
              {statuses.map(status => (
                <option key={status.value} value={status.value}>
                  {status.label}
                </option>
              ))}
            </select>

            {/* Fee Range */}
            <div className="space-y-2">
              <label className="text-sm text-gray-400 flex items-center space-x-1">
                <DollarSign className="w-4 h-4" />
                <span>Taxa: R$ {feeRange[0]} - R$ {feeRange[1]}</span>
              </label>
              <input
                type="range"
                min="0"
                max="100"
                value={feeRange[1]}
                onChange={(e) => setFeeRange([feeRange[0], parseInt(e.target.value)])}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
              />
            </div>

            {/* Sort */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-4 py-3 bg-gray-900 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            >
              <option value="startDate">Data de início</option>
              <option value="prizePool">Premiação</option>
              <option value="participants">Participantes</option>
              <option value="registrationFee">Taxa de inscrição</option>
            </select>
          </div>
        </div>
      </div>

      {/* Tournament Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredTournaments.map((tournament) => (
          <div
            key={tournament.id}
            className="bg-gray-800 rounded-xl border border-gray-700 hover:border-purple-500/50 transition-all duration-200 overflow-hidden group cursor-pointer"
            onClick={() => onTournamentDetails(tournament.id)}
          >
            {/* Tournament Cover Image */}
            <div className="h-48 overflow-hidden relative">
              <img
                src={tournament.coverImageUrl || 'https://images.unsplash.com/photo-1542751371-adc38448a05e?w=400&h=300&fit=crop&q=80'}
                alt={tournament.name}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                onError={(e) => {
                  e.currentTarget.src = 'https://images.unsplash.com/photo-1542751371-adc38448a05e?w=400&h=300&fit=crop&q=80';
                }}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
            </div>

            <div className="p-6">
              {/* Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-white mb-2 group-hover:text-purple-400 transition-colors">
                    {tournament.name}
                  </h3>
                  <div className={`inline-flex px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(tournament.status)}`}>
                    {getStatusText(tournament.status)}
                  </div>
                </div>
              </div>

              {/* Info Grid */}
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center space-x-2 text-gray-400">
                  <Trophy className="w-4 h-4" />
                  <span>R$ {tournament.prizePool.toLocaleString('pt-BR')}</span>
                </div>
                <div className="flex items-center space-x-2 text-gray-400">
                  <Users className="w-4 h-4" />
                  <span>{tournament.participants.length}/{tournament.maxParticipants}</span>
                </div>
                <div className="flex items-center space-x-2 text-gray-400">
                  <DollarSign className="w-4 h-4" />
                  <span>R$ {tournament.registrationFee.toLocaleString('pt-BR')}</span>
                </div>
                <div className="flex items-center space-x-2 text-gray-400">
                  <Calendar className="w-4 h-4" />
                  <span>{new Date(tournament.startDate).toLocaleDateString('pt-BR')}</span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredTournaments.length === 0 && (
        <div className="text-center py-16">
          <Trophy className="w-16 h-16 text-gray-600 mx-auto mb-4" />
          <h3 className="text-xl font-medium text-gray-400 mb-2">Nenhum torneio encontrado</h3>
          <p className="text-gray-500">Tente ajustar os filtros ou buscar por outros termos</p>
        </div>
      )}
    </div>
  );
}

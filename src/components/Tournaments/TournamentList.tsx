import React, { useState } from 'react';
import { GameSelection } from './GameSelection';
import { GameTournamentList } from './GameTournamentList';

interface TournamentListProps {
  onTournamentDetails?: (tournamentId: string) => void;
}

export function TournamentList({ onTournamentDetails }: TournamentListProps) {
  const [currentView, setCurrentView] = useState<'games' | 'tournaments'>('games');
  const [selectedGame, setSelectedGame] = useState<string>('');

  const handleGameSelect = (game: string) => {
    setSelectedGame(game);
    setCurrentView('tournaments');
  };

  const handleBackToGames = () => {
    setCurrentView('games');
    setSelectedGame('');
  };

  // Renderizar baseado na view atual
  if (currentView === 'games') {
    return (
      <GameSelection
        onGameSelect={handleGameSelect}
      />
    );
  }

  return (
    <GameTournamentList
      game={selectedGame}
      onBack={handleBackToGames}
      onTournamentDetails={onTournamentDetails || (() => {})}
    />
  );
}
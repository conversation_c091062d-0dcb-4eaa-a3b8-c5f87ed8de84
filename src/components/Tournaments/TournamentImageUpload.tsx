import React, { useState } from 'react';
import { Upload, Image, X, Save } from 'lucide-react';
import { apiService } from '../../services/apiService';
import { useToast } from '../../contexts/ToastContext';

interface TournamentImageUploadProps {
  tournamentId: string;
  currentBannerUrl?: string;
  currentCoverUrl?: string;
  onClose: () => void;
  onUpdate: () => void;
}

export function TournamentImageUpload({ 
  tournamentId, 
  currentBannerUrl, 
  currentCoverUrl, 
  onClose, 
  onUpdate 
}: TournamentImageUploadProps) {
  const [bannerUrl, setBannerUrl] = useState(currentBannerUrl || '');
  const [coverUrl, setCoverUrl] = useState(currentCoverUrl || '');
  const [saving, setSaving] = useState(false);
  const { toast } = useToast();

  const handleSave = async () => {
    try {
      setSaving(true);
      
      const response = await apiService.updateTournamentImages(tournamentId, {
        bannerUrl: bannerUrl || undefined,
        coverImageUrl: coverUrl || undefined
      });

      if (response.success) {
        toast.success('Imagens atualizadas com sucesso!');
        onUpdate();
        onClose();
      } else {
        toast.error(response.error || 'Erro ao atualizar imagens');
      }
    } catch (error) {
      console.error('Erro ao atualizar imagens:', error);
      toast.error('Erro ao atualizar imagens');
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <div className="bg-gray-800 rounded-xl border border-gray-700 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <h3 className="text-xl font-bold text-white">Atualizar Imagens do Torneio</h3>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-white transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Banner Image */}
          <div className="space-y-3">
            <label className="block text-sm font-medium text-gray-300">
              Banner do Torneio (800x400px recomendado)
            </label>
            <input
              type="url"
              value={bannerUrl}
              onChange={(e) => setBannerUrl(e.target.value)}
              placeholder="https://exemplo.com/banner.jpg"
              className="w-full px-4 py-3 bg-gray-900 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
            {bannerUrl && (
              <div className="relative h-32 rounded-lg overflow-hidden border border-gray-600">
                <img
                  src={bannerUrl}
                  alt="Preview do banner"
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.currentTarget.src = 'https://images.unsplash.com/photo-1542751371-adc38448a05e?w=800&h=400&fit=crop&q=80';
                  }}
                />
              </div>
            )}
          </div>

          {/* Cover Image */}
          <div className="space-y-3">
            <label className="block text-sm font-medium text-gray-300">
              Capa do Torneio (400x300px recomendado)
            </label>
            <input
              type="url"
              value={coverUrl}
              onChange={(e) => setCoverUrl(e.target.value)}
              placeholder="https://exemplo.com/capa.jpg"
              className="w-full px-4 py-3 bg-gray-900 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
            {coverUrl && (
              <div className="relative h-32 rounded-lg overflow-hidden border border-gray-600">
                <img
                  src={coverUrl}
                  alt="Preview da capa"
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.currentTarget.src = 'https://images.unsplash.com/photo-1542751371-adc38448a05e?w=400&h=300&fit=crop&q=80';
                  }}
                />
              </div>
            )}
          </div>

          {/* Info */}
          <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <Image className="w-5 h-5 text-blue-400 mt-0.5" />
              <div className="text-sm text-blue-300">
                <p className="font-medium mb-1">Dicas para melhores imagens:</p>
                <ul className="space-y-1 text-blue-200">
                  <li>• Use URLs de imagens hospedadas (imgur, cloudinary, etc.)</li>
                  <li>• Banner: 800x400px para melhor qualidade</li>
                  <li>• Capa: 400x300px para listagem de torneios</li>
                  <li>• Formatos suportados: JPG, PNG, WebP</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
          >
            Cancelar
          </button>
          <button
            onClick={handleSave}
            disabled={saving}
            className="flex items-center space-x-2 px-6 py-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-200 disabled:opacity-50"
          >
            {saving ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>Salvando...</span>
              </>
            ) : (
              <>
                <Save className="w-4 h-4" />
                <span>Salvar Imagens</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}

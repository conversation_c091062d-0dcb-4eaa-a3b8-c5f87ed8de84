import React, { useState, useEffect } from 'react';
import { Search, Filter, ArrowLeft } from 'lucide-react';
import { GameCard } from './GameCard';
import { apiService } from '../../services/apiService';

interface GameData {
  name: string;
  tournamentCount: number;
  activeCount: number;
  imageUrl: string;
}

interface GameSelectionProps {
  onGameSelect: (game: string) => void;
  onBack?: () => void;
}

// Imagens dos jogos usando URLs mais estáveis
const GAME_IMAGES: Record<string, string> = {
  'Counter-Strike 2': 'https://images.unsplash.com/photo-1542751371-adc38448a05e?w=400&h=300&fit=crop&q=80',
  'League of Legends': 'https://images.unsplash.com/photo-1511512578047-dfb367046420?w=400&h=300&fit=crop&q=80',
  'Valorant': 'https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=300&fit=crop&q=80',
  'Dota 2': 'https://images.unsplash.com/photo-1560253023-3ec5d502959f?w=400&h=300&fit=crop&q=80',
  'Rocket League': 'https://images.unsplash.com/photo-1493711662062-fa541adb3fc8?w=400&h=300&fit=crop&q=80',
  'Fortnite': 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=300&fit=crop&q=80',
  'Apex Legends': 'https://images.unsplash.com/photo-1552820728-8b83bb6b773f?w=400&h=300&fit=crop&q=80',
  'Overwatch 2': 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop&q=80',
  'FIFA 24': 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=400&h=300&fit=crop&q=80',
  'Call of Duty': 'https://images.unsplash.com/photo-1509198397868-475647b2a1e5?w=400&h=300&fit=crop&q=80',
  'VALORANT': 'https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=300&fit=crop&q=80'
};

export function GameSelection({ onGameSelect, onBack }: GameSelectionProps) {
  const [games, setGames] = useState<GameData[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    loadGames();
  }, []);

  const loadGames = async () => {
    try {
      setLoading(true);
      const response = await apiService.getTournaments();
      
      if (response.success && response.tournaments) {
        // Agrupar torneios por jogo
        const gameMap = new Map<string, { total: number; active: number }>();
        
        response.tournaments.forEach(tournament => {
          const current = gameMap.get(tournament.game) || { total: 0, active: 0 };
          current.total++;
          
          if (['registration', 'upcoming', 'ready-to-start', 'ongoing'].includes(tournament.status)) {
            current.active++;
          }
          
          gameMap.set(tournament.game, current);
        });

        // Converter para array de GameData
        const gameData: GameData[] = Array.from(gameMap.entries()).map(([name, counts]) => ({
          name,
          tournamentCount: counts.total,
          activeCount: counts.active,
          imageUrl: GAME_IMAGES[name] || 'https://images.unsplash.com/photo-1542751371-adc38448a05e?w=400&h=300&fit=crop&q=80'
        }));

        // Ordenar por número de torneios ativos, depois por total
        gameData.sort((a, b) => {
          if (a.activeCount !== b.activeCount) {
            return b.activeCount - a.activeCount;
          }
          return b.tournamentCount - a.tournamentCount;
        });

        setGames(gameData);
      }
    } catch (error) {
      console.error('Erro ao carregar jogos:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredGames = games.filter(game =>
    game.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="text-center py-16">
          <div className="w-8 h-8 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-400">Carregando jogos...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {onBack && (
            <button
              onClick={onBack}
              className="p-2 text-gray-400 hover:text-white transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
          )}
          <div>
            <h1 className="text-3xl font-bold text-white">Escolha seu Jogo</h1>
            <p className="text-gray-400 mt-1">Selecione um jogo para ver os torneios disponíveis</p>
          </div>
        </div>
      </div>

      {/* Search */}
      <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="text"
            placeholder="Buscar por jogo..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-3 bg-gray-900 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Games Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {filteredGames.map((game) => (
          <GameCard
            key={game.name}
            game={game.name}
            tournamentCount={game.tournamentCount}
            activeCount={game.activeCount}
            imageUrl={game.imageUrl}
            onClick={() => onGameSelect(game.name)}
          />
        ))}
      </div>

      {filteredGames.length === 0 && (
        <div className="text-center py-16">
          <Filter className="w-16 h-16 text-gray-600 mx-auto mb-4" />
          <h3 className="text-xl font-medium text-gray-400 mb-2">Nenhum jogo encontrado</h3>
          <p className="text-gray-500">Tente buscar por outros termos</p>
        </div>
      )}
    </div>
  );
}

import React, { useState, useEffect } from 'react';
import { Trophy, Users, Clock, CheckCircle, AlertCircle, Edit3 } from 'lucide-react';
import { apiService } from '../../services/apiService';
import { useAuth } from '../../contexts/AuthContext';
import { useToast } from '../../contexts/ToastContext';

interface Team {
  id: string;
  name: string;
  logo?: string;
}

interface Match {
  id: string;
  round: number;
  position: number;
  participant1: string | null;
  participant2: string | null;
  winner: string | null;
  scoreP1: number | null;
  scoreP2: number | null;
  status: 'PENDING' | 'ONGOING' | 'COMPLETED';
  participant1Team?: Team;
  participant2Team?: Team;
  winnerTeam?: Team;
}

interface BracketVisualizationProps {
  tournamentId: string;
  isOrganizer: boolean;
  tournamentStatus: string;
}

export function BracketVisualization({ tournamentId, isOrganizer, tournamentStatus }: BracketVisualizationProps) {
  const toast = useToast();
  const [matches, setMatches] = useState<Match[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedMatch, setSelectedMatch] = useState<Match | null>(null);
  const [resultForm, setResultForm] = useState({
    winner: '',
    scoreP1: '',
    scoreP2: ''
  });
  const [submittingResult, setSubmittingResult] = useState(false);

  useEffect(() => {
    loadMatches();
  }, [tournamentId]);

  const loadMatches = async () => {
    try {
      setLoading(true);
      const response = await apiService.getTournamentMatches(tournamentId);
      
      if (response.success) {
        setMatches(response.matches);
      } else {
        toast.error('Erro ao carregar partidas');
      }
    } catch (error) {
      console.error('Erro ao carregar partidas:', error);
      toast.error('Erro ao carregar partidas');
    } finally {
      setLoading(false);
    }
  };

  const handleMatchClick = (match: Match) => {
    if (!isOrganizer) return;

    setSelectedMatch(match);
    setResultForm({
      winner: match.winner || '',
      scoreP1: match.scoreP1?.toString() || '0',
      scoreP2: match.scoreP2?.toString() || '0'
    });
  };

  const handleSubmitResult = async () => {
    if (!selectedMatch || !resultForm.winner || !resultForm.scoreP1 || !resultForm.scoreP2) {
      toast.error('Preencha todos os campos');
      return;
    }

    try {
      setSubmittingResult(true);

      // Usar método correto baseado no status da partida
      const isEditing = selectedMatch.status === 'COMPLETED';
      const response = isEditing
        ? await apiService.editMatchResult(tournamentId, selectedMatch.id, {
            winner: resultForm.winner,
            scoreP1: parseInt(resultForm.scoreP1),
            scoreP2: parseInt(resultForm.scoreP2)
          })
        : await apiService.updateMatchResult(tournamentId, selectedMatch.id, {
            winner: resultForm.winner,
            scoreP1: parseInt(resultForm.scoreP1),
            scoreP2: parseInt(resultForm.scoreP2)
          });

      if (response.success) {
        toast.success(isEditing ? 'Resultado editado com sucesso!' : 'Resultado atualizado com sucesso!');
        setSelectedMatch(null);
        loadMatches(); // Recarregar partidas
      } else {
        toast.error(response.error || 'Erro ao processar resultado');
      }
    } catch (error) {
      console.error('Erro ao processar resultado:', error);
      toast.error('Erro ao processar resultado');
    } finally {
      setSubmittingResult(false);
    }
  };

  const getMatchStatusIcon = (match: Match) => {
    switch (match.status) {
      case 'COMPLETED':
        return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'ONGOING':
        return <Clock className="w-4 h-4 text-yellow-400" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-400" />;
    }
  };

  const getMatchStatusText = (match: Match) => {
    switch (match.status) {
      case 'COMPLETED':
        return 'Finalizada';
      case 'ONGOING':
        return 'Em andamento';
      default:
        return 'Aguardando';
    }
  };

  const groupMatchesByRound = () => {
    const grouped: { [round: number]: Match[] } = {};
    matches.forEach(match => {
      if (!grouped[match.round]) {
        grouped[match.round] = [];
      }
      grouped[match.round].push(match);
    });
    return grouped;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
        <span className="ml-3 text-gray-400">Carregando brackets...</span>
      </div>
    );
  }

  if (matches.length === 0) {
    return (
      <div className="text-center py-12">
        <Trophy className="w-16 h-16 text-gray-600 mx-auto mb-4" />
        <h3 className="text-xl font-medium text-gray-400 mb-2">Brackets não gerados</h3>
        <p className="text-gray-500">
          {tournamentStatus === 'upcoming' 
            ? 'O torneio precisa ser iniciado para gerar os brackets'
            : 'Nenhuma partida encontrada'
          }
        </p>
      </div>
    );
  }

  const roundsData = groupMatchesByRound();
  const rounds = Object.keys(roundsData).map(Number).sort((a, b) => a - b);

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-xl font-bold text-white">Brackets do Torneio</h3>
        {isOrganizer && (
          <div className="text-sm text-gray-400">
            Clique em uma partida para inserir/editar o resultado
          </div>
        )}
      </div>

      {/* Brackets Grid */}
      <div className="grid gap-8" style={{ gridTemplateColumns: `repeat(${rounds.length}, 1fr)` }}>
        {rounds.map(round => (
          <div key={round} className="space-y-4">
            <h4 className="text-lg font-semibold text-purple-400 text-center">
              {round === rounds.length ? 'Final' : 
               round === rounds.length - 1 ? 'Semifinal' : 
               `Rodada ${round}`}
            </h4>
            
            <div className="space-y-4">
              {roundsData[round].map(match => (
                <div
                  key={match.id}
                  className={`
                    bg-gray-800 rounded-lg p-4 border transition-all duration-200 relative
                    ${match.status === 'COMPLETED' ? 'border-green-500/30' : 'border-gray-700'}
                    ${isOrganizer ? 'cursor-pointer hover:border-purple-500/50 hover:bg-gray-750 hover:shadow-lg hover:scale-[1.02] group' : ''}
                  `}
                  onClick={() => handleMatchClick(match)}
                >
                  {/* Match Header */}
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-xs text-gray-400">Partida {match.position}</span>
                    <div className="flex items-center space-x-1">
                      {getMatchStatusIcon(match)}
                      <span className="text-xs text-gray-400">{getMatchStatusText(match)}</span>
                      {isOrganizer && match.participant1 && match.participant2 && (
                        <Edit3 className="w-3 h-3 text-purple-400 opacity-0 group-hover:opacity-100 transition-opacity ml-1" />
                      )}
                    </div>
                  </div>

                  {/* Teams */}
                  <div className="space-y-2">
                    {/* Team 1 */}
                    <div className={`
                      flex items-center justify-between p-2 rounded
                      ${match.winner === match.participant1 ? 'bg-green-500/20 border border-green-500/30' : 'bg-gray-700/50'}
                    `}>
                      <span className="text-white font-medium">
                        {match.participant1Team?.name || 'TBD'}
                      </span>
                      {match.status === 'COMPLETED' && (
                        <span className="text-white font-bold">{match.scoreP1}</span>
                      )}
                    </div>

                    {/* VS */}
                    <div className="text-center text-gray-400 text-sm">vs</div>

                    {/* Team 2 */}
                    <div className={`
                      flex items-center justify-between p-2 rounded
                      ${match.winner === match.participant2 ? 'bg-green-500/20 border border-green-500/30' : 'bg-gray-700/50'}
                    `}>
                      <span className="text-white font-medium">
                        {match.participant2Team?.name || 'TBD'}
                      </span>
                      {match.status === 'COMPLETED' && (
                        <span className="text-white font-bold">{match.scoreP2}</span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Modal para inserir resultado */}
      {selectedMatch && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md">
            <h3 className="text-xl font-bold text-white mb-4">
              {selectedMatch.status === 'COMPLETED' ? 'Editar' : 'Inserir'} Resultado - Partida {selectedMatch.position}
            </h3>

            <div className="space-y-4">
              {/* Seleção do vencedor */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Vencedor
                </label>
                <select
                  value={resultForm.winner}
                  onChange={(e) => setResultForm(prev => ({ ...prev, winner: e.target.value }))}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                >
                  <option value="">Selecione o vencedor</option>
                  {selectedMatch.participant1Team && (
                    <option value={selectedMatch.participant1}>
                      {selectedMatch.participant1Team.name}
                    </option>
                  )}
                  {selectedMatch.participant2Team && (
                    <option value={selectedMatch.participant2}>
                      {selectedMatch.participant2Team.name}
                    </option>
                  )}
                </select>
              </div>

              {/* Placar */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    {selectedMatch.participant1Team?.name || 'Time 1'}
                  </label>
                  <input
                    type="number"
                    min="0"
                    value={resultForm.scoreP1}
                    onChange={(e) => setResultForm(prev => ({ ...prev, scoreP1: e.target.value }))}
                    className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                    placeholder="0"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    {selectedMatch.participant2Team?.name || 'Time 2'}
                  </label>
                  <input
                    type="number"
                    min="0"
                    value={resultForm.scoreP2}
                    onChange={(e) => setResultForm(prev => ({ ...prev, scoreP2: e.target.value }))}
                    className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                    placeholder="0"
                  />
                </div>
              </div>
            </div>

            {/* Botões */}
            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => setSelectedMatch(null)}
                className="flex-1 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={handleSubmitResult}
                disabled={submittingResult}
                className="flex-1 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-500 transition-colors disabled:opacity-50"
              >
                {submittingResult ? 'Salvando...' : selectedMatch.status === 'COMPLETED' ? 'Salvar Edição' : 'Salvar Resultado'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

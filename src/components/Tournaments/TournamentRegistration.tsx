import React, { useState, useEffect } from 'react';
import { Users, User, Crown, AlertCircle, CheckCircle, GamepadIcon } from 'lucide-react';
import { apiService } from '../../services/apiService';
import { useAuth } from '../../contexts/AuthContext';

interface Team {
  id: string;
  name: string;
  game: string;
  logo?: string;
  captainId: string;
  captain: {
    id: string;
    name: string;
    email: string;
  };
  members: Array<{
    id: string;
    name: string;
    email: string;
  }>;
}

interface Tournament {
  id: string;
  name: string;
  game: string;
  maxParticipants: number;
  registrationFee: number;
  status: string;
}

interface TournamentRegistrationProps {
  tournament: Tournament;
  onRegistrationComplete: () => void;
  onCancel: () => void;
}

export function TournamentRegistration({ 
  tournament, 
  onRegistrationComplete, 
  onCancel 
}: TournamentRegistrationProps) {
  const { user } = useAuth();
  const [registrationType, setRegistrationType] = useState<'player' | 'team'>('player');
  const [availableTeams, setAvailableTeams] = useState<Team[]>([]);
  const [selectedTeamId, setSelectedTeamId] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [loadingTeams, setLoadingTeams] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    if (registrationType === 'team') {
      loadAvailableTeams();
    }
  }, [registrationType, tournament.game]);

  const loadAvailableTeams = async () => {
    try {
      setLoadingTeams(true);
      const response = await apiService.getMyTeams();
      
      if (response.success) {
        // Filtrar apenas times do jogo do torneio onde o usuário é capitão
        const gameTeams = (response.teams || []).filter((team: Team) => 
          team.game === tournament.game && team.captainId === user?.id
        );
        setAvailableTeams(gameTeams);
        
        if (gameTeams.length > 0) {
          setSelectedTeamId(gameTeams[0].id);
        }
      }
    } catch (err) {
      console.error('Erro ao carregar times:', err);
    } finally {
      setLoadingTeams(false);
    }
  };

  const handleRegistration = async () => {
    try {
      setLoading(true);
      setError(null);

      const registrationData: any = {
        participantType: registrationType
      };

      if (registrationType === 'team') {
        if (!selectedTeamId) {
          setError('Selecione um time para a inscrição');
          return;
        }
        registrationData.teamId = selectedTeamId;
      }

      const response = await apiService.registerForTournament(tournament.id, registrationData);

      if (response.success) {
        setSuccess(true);
        setTimeout(() => {
          onRegistrationComplete();
        }, 2000);
      } else {
        setError(response.error || 'Erro ao realizar inscrição');
      }
    } catch (err) {
      setError('Erro ao realizar inscrição. Tente novamente.');
      console.error('Erro na inscrição:', err);
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <div className="bg-gray-800 rounded-xl p-8 border border-gray-700">
        <div className="text-center">
          <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-white mb-2">Inscrição Realizada!</h3>
          <p className="text-gray-400 mb-4">
            Você foi inscrito com sucesso no torneio "{tournament.name}".
          </p>
          <p className="text-sm text-gray-500">
            {registrationType === 'team' ? 'Inscrição em equipe' : 'Inscrição individual'}
          </p>
        </div>
      </div>
    );
  }

  const selectedTeam = availableTeams.find(team => team.id === selectedTeamId);

  return (
    <div className="bg-gray-800 rounded-xl p-8 border border-gray-700">
      <div className="flex items-center space-x-3 mb-6">
        <GamepadIcon className="w-6 h-6 text-purple-500" />
        <h3 className="text-xl font-semibold text-white">Inscrever-se no Torneio</h3>
      </div>

      <div className="mb-6 p-4 bg-gray-700 rounded-lg">
        <h4 className="text-lg font-medium text-white mb-2">{tournament.name}</h4>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-400">Jogo:</span>
            <span className="text-white ml-2">{tournament.game}</span>
          </div>
          <div>
            <span className="text-gray-400">Taxa:</span>
            <span className="text-white ml-2">
              {tournament.registrationFee > 0 ? `R$ ${tournament.registrationFee}` : 'Gratuito'}
            </span>
          </div>
        </div>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-500/10 border border-red-500/20 rounded-lg flex items-center space-x-3">
          <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0" />
          <p className="text-red-400 text-sm">{error}</p>
        </div>
      )}

      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-3">
            Tipo de Inscrição
          </label>
          <div className="grid grid-cols-2 gap-4">
            <button
              onClick={() => setRegistrationType('player')}
              className={`p-4 rounded-lg border-2 transition-all duration-200 ${
                registrationType === 'player'
                  ? 'border-purple-500 bg-purple-500/10'
                  : 'border-gray-600 bg-gray-700 hover:border-gray-500'
              }`}
            >
              <User className="w-8 h-8 mx-auto mb-2 text-purple-500" />
              <div className="text-white font-medium">Individual</div>
              <div className="text-gray-400 text-xs">Participar sozinho</div>
            </button>

            <button
              onClick={() => setRegistrationType('team')}
              className={`p-4 rounded-lg border-2 transition-all duration-200 ${
                registrationType === 'team'
                  ? 'border-purple-500 bg-purple-500/10'
                  : 'border-gray-600 bg-gray-700 hover:border-gray-500'
              }`}
            >
              <Users className="w-8 h-8 mx-auto mb-2 text-purple-500" />
              <div className="text-white font-medium">Em Equipe</div>
              <div className="text-gray-400 text-xs">Com seu time</div>
            </button>
          </div>
        </div>

        {registrationType === 'team' && (
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-3">
              Selecionar Time
            </label>
            
            {loadingTeams ? (
              <div className="text-center py-4">
                <div className="w-6 h-6 border-2 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                <p className="text-gray-400 text-sm">Carregando times...</p>
              </div>
            ) : availableTeams.length === 0 ? (
              <div className="p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
                <p className="text-yellow-400 text-sm">
                  Você não possui times de {tournament.game} onde seja capitão.
                  Apenas capitães podem inscrever times em torneios.
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                {availableTeams.map((team) => (
                  <div
                    key={team.id}
                    className={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                      selectedTeamId === team.id
                        ? 'border-purple-500 bg-purple-500/10'
                        : 'border-gray-600 bg-gray-700 hover:border-gray-500'
                    }`}
                    onClick={() => setSelectedTeamId(team.id)}
                  >
                    <div className="flex items-center space-x-3">
                      {team.logo ? (
                        <img
                          src={team.logo}
                          alt={team.name}
                          className="w-10 h-10 rounded-lg object-cover"
                        />
                      ) : (
                        <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
                          <Users className="w-5 h-5 text-white" />
                        </div>
                      )}
                      
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <h5 className="text-white font-medium">{team.name}</h5>
                          <Crown className="w-4 h-4 text-yellow-500" />
                        </div>
                        <p className="text-gray-400 text-sm">
                          {team.members.length} membro{team.members.length !== 1 ? 's' : ''}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        <div className="flex space-x-4 pt-4">
          <button
            onClick={handleRegistration}
            disabled={loading || (registrationType === 'team' && (!selectedTeamId || availableTeams.length === 0))}
            className="flex-1 bg-gradient-to-r from-purple-600 to-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
          >
            {loading ? (
              <div className="flex items-center justify-center space-x-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>Inscrevendo...</span>
              </div>
            ) : (
              `Inscrever ${registrationType === 'team' ? 'Time' : 'Individual'}`
            )}
          </button>

          <button
            onClick={onCancel}
            disabled={loading}
            className="px-6 py-3 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 focus:ring-offset-gray-800 disabled:opacity-50 transition-colors"
          >
            Cancelar
          </button>
        </div>
      </div>
    </div>
  );
}

import React from 'react';

interface LoadingSpinnerProps {
  message?: string;
  size?: 'sm' | 'md' | 'lg';
}

export function LoadingSpinner({ message = 'Carregando...', size = 'md' }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-16 h-16',
    lg: 'w-24 h-24'
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center">
      <div className="text-center">
        <div className={`${sizeClasses[size]} border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4`}></div>
        <p className="text-white text-lg font-medium">{message}</p>
        <p className="text-gray-400 text-sm mt-2">🎮 CAMPMAKER</p>
      </div>
    </div>
  );
}

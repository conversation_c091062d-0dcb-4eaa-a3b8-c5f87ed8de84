import React, { useRef, useEffect } from 'react';
import * as THREE from 'three';

interface Laptop3DProps {
  scrollProgress: number;
}

export function Laptop3D({ scrollProgress }: Laptop3DProps) {
  const mountRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene>();
  const rendererRef = useRef<THREE.WebGLRenderer>();
  const cameraRef = useRef<THREE.PerspectiveCamera>();
  const laptopGroupRef = useRef<THREE.Group>();
  const screenRef = useRef<THREE.Mesh>();

  useEffect(() => {
    if (!mountRef.current) return;

    // Scene setup
    const scene = new THREE.Scene();
    sceneRef.current = scene;

    // Camera setup
    const camera = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      1000
    );
    camera.position.set(0, 1, 8);
    camera.lookAt(0, 0, 0);
    cameraRef.current = camera;

    // Renderer setup
    const renderer = new THREE.WebGLRenderer({ 
      antialias: true, 
      alpha: true 
    });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setClearColor(0x000000, 0);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    rendererRef.current = renderer;

    mountRef.current.appendChild(renderer.domElement);

    // Lighting
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(5, 5, 5);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    scene.add(directionalLight);

    // Gaming RGB lights
    const rgbLight1 = new THREE.PointLight(0x00ff88, 0.5, 10);
    rgbLight1.position.set(-2, 1, 2);
    scene.add(rgbLight1);

    const rgbLight2 = new THREE.PointLight(0x8800ff, 0.5, 10);
    rgbLight2.position.set(2, 1, 2);
    scene.add(rgbLight2);

    // Create laptop group
    const laptopGroup = new THREE.Group();
    laptopGroupRef.current = laptopGroup;
    scene.add(laptopGroup);

    // Laptop base (bottom part) - Maior
    const baseGeometry = new THREE.BoxGeometry(6, 0.3, 4.5);
    const baseMaterial = new THREE.MeshPhongMaterial({
      color: 0x2a2a2a,
      shininess: 100
    });
    const base = new THREE.Mesh(baseGeometry, baseMaterial);
    base.position.y = -0.15;
    base.castShadow = true;
    base.receiveShadow = true;
    laptopGroup.add(base);

    // Keyboard area - Maior
    const keyboardGeometry = new THREE.BoxGeometry(5.2, 0.08, 3.7);
    const keyboardMaterial = new THREE.MeshPhongMaterial({ color: 0x1a1a1a });
    const keyboard = new THREE.Mesh(keyboardGeometry, keyboardMaterial);
    keyboard.position.set(0, 0.04, 0.3);
    laptopGroup.add(keyboard);

    // WASD Keys - Maiores
    const keyGeometry = new THREE.BoxGeometry(0.3, 0.08, 0.3);
    const keyMaterial = new THREE.MeshPhongMaterial({ color: 0x444444 });

    const wKey = new THREE.Mesh(keyGeometry, keyMaterial);
    wKey.position.set(-0.3, 0.12, -0.3);
    laptopGroup.add(wKey);

    const aKey = new THREE.Mesh(keyGeometry, keyMaterial);
    aKey.position.set(-0.6, 0.12, 0);
    laptopGroup.add(aKey);

    const sKey = new THREE.Mesh(keyGeometry, keyMaterial);
    sKey.position.set(-0.3, 0.12, 0);
    laptopGroup.add(sKey);

    const dKey = new THREE.Mesh(keyGeometry, keyMaterial);
    dKey.position.set(0, 0.12, 0);
    laptopGroup.add(dKey);

    // Trackpad - Maior
    const trackpadGeometry = new THREE.BoxGeometry(1.5, 0.03, 1);
    const trackpadMaterial = new THREE.MeshPhongMaterial({ color: 0x333333 });
    const trackpad = new THREE.Mesh(trackpadGeometry, trackpadMaterial);
    trackpad.position.set(0, 0.05, 1.2);
    laptopGroup.add(trackpad);

    // Screen (starts closed) - Maior e melhor posicionada
    const screenGeometry = new THREE.BoxGeometry(6, 3.8, 0.15);
    const screenMaterial = new THREE.MeshPhongMaterial({ color: 0x1a1a1a });
    const screen = new THREE.Mesh(screenGeometry, screenMaterial);
    screen.position.set(0, 1.9, -2.25); // Posição inicial conectada à base
    screen.rotation.x = Math.PI / 2; // Start closed (90 degrees)
    screenRef.current = screen;
    laptopGroup.add(screen);

    // Screen display - Maior
    const displayGeometry = new THREE.PlaneGeometry(5.5, 3.3);

    // Create canvas for screen content
    const canvas = document.createElement('canvas');
    canvas.width = 1024;
    canvas.height = 512;
    const ctx = canvas.getContext('2d')!;
    
    // Gaming interface - Ajustado para tela maior
    const gradient = ctx.createLinearGradient(0, 0, 1024, 512);
    gradient.addColorStop(0, '#3b82f6');
    gradient.addColorStop(0.5, '#8b5cf6');
    gradient.addColorStop(1, '#ec4899');

    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, 1024, 512);

    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 48px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('E-SPORTS PR', 512, 200);

    ctx.font = '32px Arial';
    ctx.fillStyle = '#10b981';
    ctx.fillText('READY TO PLAY', 512, 260);

    // Gaming dots - Maiores
    ctx.fillStyle = '#10b981';
    ctx.beginPath();
    ctx.arc(400, 360, 10, 0, Math.PI * 2);
    ctx.fill();

    ctx.fillStyle = '#3b82f6';
    ctx.beginPath();
    ctx.arc(512, 360, 10, 0, Math.PI * 2);
    ctx.fill();

    ctx.fillStyle = '#8b5cf6';
    ctx.beginPath();
    ctx.arc(624, 360, 10, 0, Math.PI * 2);
    ctx.fill();

    const texture = new THREE.CanvasTexture(canvas);
    const displayMaterial = new THREE.MeshBasicMaterial({ 
      map: texture,
      transparent: true,
      opacity: 0
    });
    const display = new THREE.Mesh(displayGeometry, displayMaterial);
    display.position.set(0, 0, 0.06);
    screen.add(display);

    // Animation loop
    const animate = () => {
      requestAnimationFrame(animate);
      
      // RGB lighting animation
      const time = Date.now() * 0.001;
      rgbLight1.color.setHSL((time * 0.1) % 1, 1, 0.5);
      rgbLight2.color.setHSL((time * 0.1 + 0.5) % 1, 1, 0.5);
      
      renderer.render(scene, camera);
    };
    animate();

    // Handle resize
    const handleResize = () => {
      if (!camera || !renderer) return;
      camera.aspect = window.innerWidth / window.innerHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(window.innerWidth, window.innerHeight);
    };
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (mountRef.current && renderer.domElement) {
        mountRef.current.removeChild(renderer.domElement);
      }
      renderer.dispose();
    };
  }, []);

  // Update laptop animation based on scroll
  useEffect(() => {
    if (!screenRef.current || !laptopGroupRef.current) return;

    // Screen opening animation (from 90 degrees to 0) - Mais lento
    const openProgress = Math.min(scrollProgress * 1.2, 1);
    const screenAngle = (Math.PI / 2) * (1 - openProgress);
    screenRef.current.rotation.x = screenAngle;

    // Screen position adjustment as it opens - Melhor conexão
    const screenZ = -2.25 + (openProgress * 0.8);
    const screenY = 1.9 + (openProgress * 0.5);
    screenRef.current.position.z = screenZ;
    screenRef.current.position.y = screenY;

    // Display opacity
    const display = screenRef.current.children[0] as THREE.Mesh;
    if (display && display.material) {
      (display.material as THREE.MeshBasicMaterial).opacity = openProgress;
    }

    // Laptop scaling and positioning - Maior e melhor posicionado
    const scale = 0.8 + (scrollProgress * 0.4);
    laptopGroupRef.current.scale.setScalar(scale);

    const yPosition = -1 + (scrollProgress * -0.5); // Posiciona atrás do texto
    laptopGroupRef.current.position.y = yPosition;

    // Camera movement - Mais suave
    if (cameraRef.current) {
      cameraRef.current.position.y = 1 + (scrollProgress * 0.5);
      cameraRef.current.position.z = 8 - (scrollProgress * 1);
      cameraRef.current.lookAt(0, yPosition, 0);
    }
  }, [scrollProgress]);

  return (
    <div
      ref={mountRef}
      className="absolute inset-0 pointer-events-none"
      style={{ zIndex: 0 }}
    />
  );
}

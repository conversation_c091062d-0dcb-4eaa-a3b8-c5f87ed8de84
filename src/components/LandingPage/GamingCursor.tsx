import React, { useEffect, useRef, useState } from 'react';

export function GamingCursor() {
  const dotRef = useRef<HTMLDivElement>(null);
  const outlineRef = useRef<HTMLDivElement>(null);
  const [isHovering, setIsHovering] = useState(false);
  const [isClicking, setIsClicking] = useState(false);
  const [isCrosshair, setIsCrosshair] = useState(false);
  const trailsRef = useRef<HTMLDivElement[]>([]);

  useEffect(() => {
    const dot = dotRef.current;
    const outline = outlineRef.current;
    
    if (!dot || !outline) return;

    let mouseX = 0;
    let mouseY = 0;
    let outlineX = 0;
    let outlineY = 0;

    // Função para criar trail gaming
    const createTrail = (x: number, y: number) => {
      const trail = document.createElement('div');
      trail.className = 'cursor-trail';
      trail.style.left = x - 3 + 'px';
      trail.style.top = y - 3 + 'px';

      // Cores aleatórias gaming
      const colors = ['#3b82f6', '#8b5cf6', '#10b981', '#f59e0b', '#ec4899'];
      const randomColor = colors[Math.floor(Math.random() * colors.length)];
      trail.style.background = randomColor;
      trail.style.boxShadow = `0 0 10px ${randomColor}`;

      document.body.appendChild(trail);

      // Remover trail após animação
      setTimeout(() => {
        if (document.body.contains(trail)) {
          document.body.removeChild(trail);
        }
      }, 500);
    };

    // Função para criar partículas de clique
    const createClickParticles = (x: number, y: number) => {
      for (let i = 0; i < 6; i++) {
        const particle = document.createElement('div');
        particle.style.position = 'fixed';
        particle.style.left = x + 'px';
        particle.style.top = y + 'px';
        particle.style.width = '4px';
        particle.style.height = '4px';
        particle.style.background = '#ec4899';
        particle.style.borderRadius = '50%';
        particle.style.pointerEvents = 'none';
        particle.style.zIndex = '9996';

        const angle = (i / 6) * Math.PI * 2;
        const velocity = 50 + Math.random() * 30;
        const vx = Math.cos(angle) * velocity;
        const vy = Math.sin(angle) * velocity;

        particle.style.animation = `particle-explode-${i} 0.6s ease-out forwards`;

        // Criar animação única para cada partícula
        const keyframes = `
          @keyframes particle-explode-${i} {
            0% {
              transform: translate(0, 0) scale(1);
              opacity: 1;
            }
            100% {
              transform: translate(${vx}px, ${vy}px) scale(0);
              opacity: 0;
            }
          }
        `;

        const style = document.createElement('style');
        style.textContent = keyframes;
        document.head.appendChild(style);

        document.body.appendChild(particle);

        // Cleanup
        setTimeout(() => {
          if (document.body.contains(particle)) {
            document.body.removeChild(particle);
          }
          if (document.head.contains(style)) {
            document.head.removeChild(style);
          }
        }, 600);
      }
    };

    // Movimento do mouse
    const handleMouseMove = (e: MouseEvent) => {
      mouseX = e.clientX;
      mouseY = e.clientY;
      
      // Atualizar posição do dot imediatamente
      dot.style.left = mouseX - 4 + 'px';
      dot.style.top = mouseY - 4 + 'px';
      
      // Criar trail ocasionalmente
      if (Math.random() > 0.8) {
        createTrail(mouseX, mouseY);
      }
    };

    // Animação suave do outline
    const animateOutline = () => {
      outlineX += (mouseX - outlineX) * 0.15;
      outlineY += (mouseY - outlineY) * 0.15;
      
      outline.style.left = outlineX - 20 + 'px';
      outline.style.top = outlineY - 20 + 'px';
      
      requestAnimationFrame(animateOutline);
    };

    // Detectar hover em elementos interativos
    const handleMouseEnter = (e: Event) => {
      const target = e.target as HTMLElement;
      if (target.tagName === 'BUTTON' || target.tagName === 'A' || target.classList.contains('cursor-pointer')) {
        if (target.classList.contains('bg-gradient-to-r') || target.textContent?.includes('Começar') || target.textContent?.includes('Ver Campeonatos')) {
          setIsCrosshair(true);
        } else {
          setIsHovering(true);
        }
      }
    };

    const handleMouseLeave = (e: Event) => {
      const target = e.target as HTMLElement;
      if (target.tagName === 'BUTTON' || target.tagName === 'A' || target.classList.contains('cursor-pointer')) {
        setIsHovering(false);
        setIsCrosshair(false);
      }
    };

    // Detectar cliques com efeitos
    const handleMouseDown = (e: MouseEvent) => {
      setIsClicking(true);
      createClickParticles(e.clientX, e.clientY);
    };

    const handleMouseUp = () => {
      setIsClicking(false);
    };

    // Event listeners
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mousedown', handleMouseDown);
    document.addEventListener('mouseup', handleMouseUp);
    
    // Adicionar listeners para todos os elementos interativos
    const interactiveElements = document.querySelectorAll('button, a, [role="button"]');
    interactiveElements.forEach(el => {
      el.addEventListener('mouseenter', handleMouseEnter);
      el.addEventListener('mouseleave', handleMouseLeave);
    });

    // Iniciar animação do outline
    animateOutline();

    // Cleanup
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mousedown', handleMouseDown);
      document.removeEventListener('mouseup', handleMouseUp);
      
      interactiveElements.forEach(el => {
        el.removeEventListener('mouseenter', handleMouseEnter);
        el.removeEventListener('mouseleave', handleMouseLeave);
      });
    };
  }, []);

  // Aplicar classes baseadas no estado
  useEffect(() => {
    const body = document.body;
    
    // Remover todas as classes de cursor
    body.classList.remove('cursor-hover', 'cursor-click', 'cursor-crosshair');
    
    // Adicionar classe apropriada
    if (isClicking) {
      body.classList.add('cursor-click');
    } else if (isCrosshair) {
      body.classList.add('cursor-crosshair');
    } else if (isHovering) {
      body.classList.add('cursor-hover');
    }
  }, [isHovering, isClicking, isCrosshair]);

  // Adicionar classe gaming-cursor ao body
  useEffect(() => {
    document.body.classList.add('gaming-cursor');
    
    return () => {
      document.body.classList.remove('gaming-cursor', 'cursor-hover', 'cursor-click', 'cursor-crosshair');
    };
  }, []);

  return (
    <>
      <div ref={dotRef} className="cursor-dot" />
      <div ref={outlineRef} className="cursor-outline" />
    </>
  );
}

import React, { useState, useEffect } from 'react';
import { ChevronDown, Shield, Trophy, Users, Zap, DollarSign, MessageCircle, Star, ArrowRight, Menu, X } from 'lucide-react';
import { Laptop3D } from './Laptop3D';

interface LandingPageProps {
  onNavigateToLogin: () => void;
  onNavigateToRegister: () => void;
}

export function LandingPage({ onNavigateToLogin, onNavigateToRegister }: LandingPageProps) {
  const [scrollY, setScrollY] = useState(0);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    const handleScroll = () => setScrollY(window.scrollY);
    window.addEventListener('scroll', handleScroll);
    setIsLoaded(true);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    setMobileMenuOpen(false);
  };

  return (
    <div className={`min-h-screen bg-gray-900 text-white overflow-x-hidden transition-opacity duration-1000 ${isLoaded ? 'opacity-100' : 'opacity-0'}`}>
      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 bg-gray-900/95 backdrop-blur-sm border-b border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <Trophy className="h-8 w-8 text-blue-500" />
              <span className="text-xl font-bold">E-sports PR</span>
            </div>
            <div className="hidden md:flex items-center space-x-8">
              <button onClick={() => scrollToSection('jogos')} className="hover:text-blue-400 transition-colors">Jogos</button>
              <button onClick={() => scrollToSection('para-jogadores')} className="hover:text-blue-400 transition-colors">Para Jogadores</button>
              <button onClick={() => scrollToSection('para-organizadores')} className="hover:text-blue-400 transition-colors">Para Organizadores</button>
              <button onClick={() => scrollToSection('sobre')} className="hover:text-blue-400 transition-colors">Sobre</button>
            </div>
            <div className="flex items-center space-x-4">
              <div className="hidden md:flex items-center space-x-4">
                <button
                  onClick={onNavigateToLogin}
                  className="text-gray-300 hover:text-white transition-colors"
                >
                  Entrar
                </button>
                <button
                  onClick={onNavigateToRegister}
                  className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg transition-colors"
                >
                  Cadastrar
                </button>
              </div>

              {/* Mobile menu button */}
              <button
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                className="md:hidden text-gray-300 hover:text-white transition-colors"
              >
                {mobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile menu */}
        {mobileMenuOpen && (
          <div className="md:hidden bg-gray-900/95 backdrop-blur-sm border-t border-gray-800">
            <div className="px-4 py-4 space-y-4">
              <button onClick={() => scrollToSection('jogos')} className="block w-full text-left text-gray-300 hover:text-white transition-colors">Jogos</button>
              <button onClick={() => scrollToSection('para-jogadores')} className="block w-full text-left text-gray-300 hover:text-white transition-colors">Para Jogadores</button>
              <button onClick={() => scrollToSection('para-organizadores')} className="block w-full text-left text-gray-300 hover:text-white transition-colors">Para Organizadores</button>
              <button onClick={() => scrollToSection('sobre')} className="block w-full text-left text-gray-300 hover:text-white transition-colors">Sobre</button>
              <div className="pt-4 border-t border-gray-800 space-y-2">
                <button
                  onClick={onNavigateToLogin}
                  className="block w-full text-left text-gray-300 hover:text-white transition-colors"
                >
                  Entrar
                </button>
                <button
                  onClick={onNavigateToRegister}
                  className="block w-full bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg transition-colors text-center"
                >
                  Cadastrar
                </button>
              </div>
            </div>
          </div>
        )}
      </nav>

      {/* Hero Section with 3D Gaming Laptop */}
      <section className="relative h-[200vh] overflow-hidden">
        {/* Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900" />

        {/* 3D Laptop Scene */}
        <div className="sticky top-0 h-screen">
          <Laptop3D scrollProgress={Math.min(scrollY / 1000, 1)} />

        {/* Text Content */}
        <div className="sticky top-0 h-screen flex items-center justify-center">
          <div
            className="absolute inset-0 flex items-center justify-center pointer-events-none z-10"
            style={{
              opacity: Math.max(1 - (scrollY * 0.003), 0),
            }}
          >
            <div className="text-center max-w-4xl mx-auto px-4">
              <h1 className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
                E-sports PR
              </h1>
              <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-2xl mx-auto">
                A plataforma mais segura e completa para campeonatos de e-sports no Paraná
              </p>
              <p className="text-lg text-gray-400 mb-12 max-w-3xl mx-auto">
                Organize torneios profissionais ou participe de campeonatos com total segurança.
                Pagamentos garantidos, sem risco de calote, com a melhor experiência para jogadores e organizadores.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center pointer-events-auto">
                <button
                  onClick={onNavigateToRegister}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 px-8 py-4 rounded-lg text-lg font-semibold transition-all transform hover:scale-105 flex items-center gap-2"
                >
                  Começar Agora
                  <ArrowRight className="h-5 w-5" />
                </button>
                <button
                  onClick={onNavigateToLogin}
                  className="border border-gray-600 hover:border-gray-500 px-8 py-4 rounded-lg text-lg font-semibold transition-all hover:bg-gray-800"
                >
                  Ver Campeonatos
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Scroll indicator */}
        <div
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce z-10"
          style={{
            opacity: Math.max(1 - (scrollY * 0.005), 0),
          }}
        >
          <button onClick={() => scrollToSection('para-jogadores')} className="text-gray-400 hover:text-white transition-colors">
            <ChevronDown className="h-8 w-8" />
          </button>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-gray-800/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl md:text-4xl font-bold text-blue-400 mb-2">500+</div>
              <div className="text-gray-400">Jogadores Ativos</div>
            </div>
            <div>
              <div className="text-3xl md:text-4xl font-bold text-green-400 mb-2">100+</div>
              <div className="text-gray-400">Torneios Realizados</div>
            </div>
            <div>
              <div className="text-3xl md:text-4xl font-bold text-purple-400 mb-2">R$ 50k+</div>
              <div className="text-gray-400">Em Prêmios Pagos</div>
            </div>
            <div>
              <div className="text-3xl md:text-4xl font-bold text-yellow-400 mb-2">0</div>
              <div className="text-gray-400">Calotes Registrados</div>
            </div>
          </div>
        </div>
      </section>

      {/* Por que jogar na E-sports PR */}
      <section id="para-jogadores" className="py-20 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent">
              Por que jogar na E-sports PR?
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Sua segurança e experiência são nossas prioridades. Jogue com tranquilidade total.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-gradient-to-br from-gray-800 to-gray-900 p-8 rounded-2xl border border-gray-700 hover:border-green-500 transition-all transform hover:scale-105">
              <div className="bg-green-500/20 w-16 h-16 rounded-full flex items-center justify-center mb-6">
                <Shield className="h-8 w-8 text-green-400" />
              </div>
              <h3 className="text-2xl font-bold mb-4 text-green-400">Dinheiro Seguro</h3>
              <p className="text-gray-400 leading-relaxed">
                Seu dinheiro fica protegido em nossa plataforma. Utilizamos sistemas bancários seguros
                e só liberamos os prêmios após a confirmação dos resultados.
              </p>
            </div>

            <div className="bg-gradient-to-br from-gray-800 to-gray-900 p-8 rounded-2xl border border-gray-700 hover:border-blue-500 transition-all transform hover:scale-105">
              <div className="bg-blue-500/20 w-16 h-16 rounded-full flex items-center justify-center mb-6">
                <Star className="h-8 w-8 text-blue-400" />
              </div>
              <h3 className="text-2xl font-bold mb-4 text-blue-400">Zero Calote</h3>
              <p className="text-gray-400 leading-relaxed">
                Nunca mais se preocupe com organizadores que somem com o dinheiro.
                Garantimos 100% dos pagamentos para todos os vencedores.
              </p>
            </div>

            <div className="bg-gradient-to-br from-gray-800 to-gray-900 p-8 rounded-2xl border border-gray-700 hover:border-purple-500 transition-all transform hover:scale-105">
              <div className="bg-purple-500/20 w-16 h-16 rounded-full flex items-center justify-center mb-6">
                <Users className="h-8 w-8 text-purple-400" />
              </div>
              <h3 className="text-2xl font-bold mb-4 text-purple-400">Campeonatos Revisados</h3>
              <p className="text-gray-400 leading-relaxed">
                Todos os torneios passam por nossa análise de segurança. Regras claras,
                organizadores verificados e ambiente justo para todos.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Por que organizar conosco */}
      <section id="para-organizadores" className="py-20 bg-gradient-to-br from-gray-800 to-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              Por que organizar conosco?
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Foque no que importa: criar torneios incríveis. Deixe a parte técnica e financeira conosco.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-gradient-to-br from-gray-700 to-gray-800 p-6 rounded-xl border border-gray-600 hover:border-purple-500 transition-all">
              <div className="bg-green-500/20 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                <DollarSign className="h-6 w-6 text-green-400" />
              </div>
              <h3 className="text-xl font-bold mb-3 text-green-400">Taxa Mínima</h3>
              <p className="text-gray-400 text-sm">
                Cobramos apenas uma pequena taxa para manter a plataforma. Você fica com a maior parte da receita.
              </p>
            </div>

            <div className="bg-gradient-to-br from-gray-700 to-gray-800 p-6 rounded-xl border border-gray-600 hover:border-blue-500 transition-all">
              <div className="bg-blue-500/20 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                <Zap className="h-6 w-6 text-blue-400" />
              </div>
              <h3 className="text-xl font-bold mb-3 text-blue-400">Sorteio Automático</h3>
              <p className="text-gray-400 text-sm">
                Não se preocupe com brackets e sorteios. Nossa IA organiza tudo automaticamente de forma justa.
              </p>
            </div>

            <div className="bg-gradient-to-br from-gray-700 to-gray-800 p-6 rounded-xl border border-gray-600 hover:border-yellow-500 transition-all">
              <div className="bg-yellow-500/20 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                <Trophy className="h-6 w-6 text-yellow-400" />
              </div>
              <h3 className="text-xl font-bold mb-3 text-yellow-400">Fácil Organização</h3>
              <p className="text-gray-400 text-sm">
                Interface intuitiva para criar e gerenciar torneios. Tudo que você precisa em um só lugar.
              </p>
            </div>

            <div className="bg-gradient-to-br from-gray-700 to-gray-800 p-6 rounded-xl border border-gray-600 hover:border-purple-500 transition-all">
              <div className="bg-purple-500/20 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                <MessageCircle className="h-6 w-6 text-purple-400" />
              </div>
              <h3 className="text-xl font-bold mb-3 text-purple-400">Chat Integrado</h3>
              <p className="text-gray-400 text-sm">
                Chat para o campeonato todo ou por confronto. Organize tudo sem sair da plataforma.
              </p>
            </div>

            <div className="bg-gradient-to-br from-gray-700 to-gray-800 p-6 rounded-xl border border-gray-600 hover:border-green-500 transition-all">
              <div className="bg-green-500/20 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                <Shield className="h-6 w-6 text-green-400" />
              </div>
              <h3 className="text-xl font-bold mb-3 text-green-400">Pagamento Seguro</h3>
              <p className="text-gray-400 text-sm">
                Não se preocupe com pagamentos. Cuidamos de tudo e você recebe no mesmo dia.
              </p>
            </div>

            <div className="bg-gradient-to-br from-gray-700 to-gray-800 p-6 rounded-xl border border-gray-600 hover:border-pink-500 transition-all">
              <div className="bg-pink-500/20 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                <Star className="h-6 w-6 text-pink-400" />
              </div>
              <h3 className="text-xl font-bold mb-3 text-pink-400">Ganhe Organizando</h3>
              <p className="text-gray-400 text-sm">
                Crie, defina regras, chame geral e ganhe dinheiro. Transforme sua paixão em renda.
              </p>
            </div>
          </div>

          <div className="text-center mt-12">
            <button
              onClick={onNavigateToRegister}
              className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 px-8 py-4 rounded-lg text-lg font-semibold transition-all transform hover:scale-105 inline-flex items-center gap-2"
            >
              Começar a Organizar
              <ArrowRight className="h-5 w-5" />
            </button>
          </div>
        </div>
      </section>

      {/* Jogos Suportados */}
      <section id="jogos" className="py-20 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-blue-400 to-green-400 bg-clip-text text-transparent">
              Jogos Suportados
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Organize torneios dos principais e-sports do momento. Mais jogos sendo adicionados constantemente.
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {/* Counter-Strike 2 */}
            <div className="bg-gradient-to-br from-orange-500/20 to-red-500/20 p-6 rounded-xl border border-orange-500/30 hover:border-orange-400 transition-all transform hover:scale-105 group">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-orange-500/30 rounded-full flex items-center justify-center group-hover:bg-orange-500/50 transition-colors">
                  <span className="text-2xl font-bold text-orange-400">CS2</span>
                </div>
                <h3 className="text-lg font-bold text-orange-400 mb-2">Counter-Strike 2</h3>
                <p className="text-sm text-gray-400">O FPS mais competitivo do mundo</p>
              </div>
            </div>

            {/* Valorant */}
            <div className="bg-gradient-to-br from-red-500/20 to-pink-500/20 p-6 rounded-xl border border-red-500/30 hover:border-red-400 transition-all transform hover:scale-105 group">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-red-500/30 rounded-full flex items-center justify-center group-hover:bg-red-500/50 transition-colors">
                  <span className="text-2xl font-bold text-red-400">VAL</span>
                </div>
                <h3 className="text-lg font-bold text-red-400 mb-2">Valorant</h3>
                <p className="text-sm text-gray-400">Tático com habilidades únicas</p>
              </div>
            </div>

            {/* League of Legends */}
            <div className="bg-gradient-to-br from-blue-500/20 to-cyan-500/20 p-6 rounded-xl border border-blue-500/30 hover:border-blue-400 transition-all transform hover:scale-105 group">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-blue-500/30 rounded-full flex items-center justify-center group-hover:bg-blue-500/50 transition-colors">
                  <span className="text-2xl font-bold text-blue-400">LoL</span>
                </div>
                <h3 className="text-lg font-bold text-blue-400 mb-2">League of Legends</h3>
                <p className="text-sm text-gray-400">MOBA mais popular do mundo</p>
              </div>
            </div>

            {/* Rocket League */}
            <div className="bg-gradient-to-br from-yellow-500/20 to-orange-500/20 p-6 rounded-xl border border-yellow-500/30 hover:border-yellow-400 transition-all transform hover:scale-105 group">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-yellow-500/30 rounded-full flex items-center justify-center group-hover:bg-yellow-500/50 transition-colors">
                  <span className="text-2xl font-bold text-yellow-400">RL</span>
                </div>
                <h3 className="text-lg font-bold text-yellow-400 mb-2">Rocket League</h3>
                <p className="text-sm text-gray-400">Futebol com carros voadores</p>
              </div>
            </div>

            {/* Free Fire */}
            <div className="bg-gradient-to-br from-purple-500/20 to-pink-500/20 p-6 rounded-xl border border-purple-500/30 hover:border-purple-400 transition-all transform hover:scale-105 group">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-purple-500/30 rounded-full flex items-center justify-center group-hover:bg-purple-500/50 transition-colors">
                  <span className="text-2xl font-bold text-purple-400">FF</span>
                </div>
                <h3 className="text-lg font-bold text-purple-400 mb-2">Free Fire</h3>
                <p className="text-sm text-gray-400">Battle Royale mobile</p>
              </div>
            </div>

            {/* FIFA */}
            <div className="bg-gradient-to-br from-green-500/20 to-emerald-500/20 p-6 rounded-xl border border-green-500/30 hover:border-green-400 transition-all transform hover:scale-105 group">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-green-500/30 rounded-full flex items-center justify-center group-hover:bg-green-500/50 transition-colors">
                  <span className="text-2xl font-bold text-green-400">FIFA</span>
                </div>
                <h3 className="text-lg font-bold text-green-400 mb-2">EA FC 24</h3>
                <p className="text-sm text-gray-400">Simulador de futebol</p>
              </div>
            </div>

            {/* Fortnite */}
            <div className="bg-gradient-to-br from-indigo-500/20 to-purple-500/20 p-6 rounded-xl border border-indigo-500/30 hover:border-indigo-400 transition-all transform hover:scale-105 group">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-indigo-500/30 rounded-full flex items-center justify-center group-hover:bg-indigo-500/50 transition-colors">
                  <span className="text-2xl font-bold text-indigo-400">FN</span>
                </div>
                <h3 className="text-lg font-bold text-indigo-400 mb-2">Fortnite</h3>
                <p className="text-sm text-gray-400">Battle Royale com construção</p>
              </div>
            </div>

            {/* Em Breve */}
            <div className="bg-gradient-to-br from-gray-600/20 to-gray-700/20 p-6 rounded-xl border border-gray-600/30 hover:border-gray-500 transition-all transform hover:scale-105 group">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-gray-600/30 rounded-full flex items-center justify-center group-hover:bg-gray-600/50 transition-colors">
                  <span className="text-xl font-bold text-gray-400">+</span>
                </div>
                <h3 className="text-lg font-bold text-gray-400 mb-2">Em Breve</h3>
                <p className="text-sm text-gray-500">Mais jogos chegando</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Sobre Nós */}
      <section id="sobre" className="py-20 bg-gradient-to-br from-gray-800 to-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">
                Sobre a E-sports PR
              </h2>
              <p className="text-xl text-gray-300 mb-6">
                Somos a primeira plataforma paranaense dedicada exclusivamente aos e-sports,
                criada por gamers para gamers.
              </p>
              <p className="text-gray-400 mb-6 leading-relaxed">
                Nossa missão é profissionalizar o cenário competitivo do Paraná, oferecendo
                uma plataforma segura, transparente e justa para todos os envolvidos.
                Acreditamos que todo jogador merece a oportunidade de competir em alto nível
                sem se preocupar com questões financeiras ou organizacionais.
              </p>
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                  <span className="text-gray-300">Fundada em 2024 por entusiastas de e-sports</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span className="text-gray-300">Foco total na segurança e transparência</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                  <span className="text-gray-300">Tecnologia de ponta para melhor experiência</span>
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="bg-gradient-to-br from-blue-500/20 to-purple-500/20 p-8 rounded-2xl border border-blue-500/30">
                <h3 className="text-2xl font-bold mb-6 text-center text-blue-400">Como Funcionamos</h3>
                <div className="space-y-6">
                  <div className="flex items-start gap-4">
                    <div className="bg-blue-500/30 w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <span className="text-sm font-bold text-blue-400">1</span>
                    </div>
                    <div>
                      <h4 className="font-semibold text-white mb-1">Organizador Cria Torneio</h4>
                      <p className="text-sm text-gray-400">Define regras, prêmios e taxa de inscrição</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="bg-green-500/30 w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <span className="text-sm font-bold text-green-400">2</span>
                    </div>
                    <div>
                      <h4 className="font-semibold text-white mb-1">Jogadores Se Inscrevem</h4>
                      <p className="text-sm text-gray-400">Pagamento seguro direto na plataforma</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="bg-purple-500/30 w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <span className="text-sm font-bold text-purple-400">3</span>
                    </div>
                    <div>
                      <h4 className="font-semibold text-white mb-1">Torneio Acontece</h4>
                      <p className="text-sm text-gray-400">Brackets automáticos e chat integrado</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="bg-yellow-500/30 w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <span className="text-sm font-bold text-yellow-400">4</span>
                    </div>
                    <div>
                      <h4 className="font-semibold text-white mb-1">Pagamentos Automáticos</h4>
                      <p className="text-sm text-gray-400">Prêmios liberados instantaneamente</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Final */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
            Pronto para Começar?
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Junte-se à revolução dos e-sports no Paraná. Seja jogador ou organizador,
            sua jornada competitiva começa aqui.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={onNavigateToRegister}
              className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 rounded-lg text-lg font-semibold transition-all transform hover:scale-105"
            >
              Criar Conta Grátis
            </button>
            <button
              onClick={onNavigateToLogin}
              className="border-2 border-white text-white hover:bg-white hover:text-blue-600 px-8 py-4 rounded-lg text-lg font-semibold transition-all"
            >
              Já Tenho Conta
            </button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 border-t border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="md:col-span-2">
              <div className="flex items-center space-x-2 mb-4">
                <Trophy className="h-8 w-8 text-blue-500" />
                <span className="text-xl font-bold text-white">E-sports PR</span>
              </div>
              <p className="text-gray-400 mb-4 max-w-md">
                A plataforma mais segura e completa para campeonatos de e-sports no Paraná.
                Criada por gamers, para gamers.
              </p>
              <div className="flex space-x-4">
                <div className="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-gray-700 transition-colors cursor-pointer">
                  <span className="text-blue-400 font-bold">f</span>
                </div>
                <div className="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-gray-700 transition-colors cursor-pointer">
                  <span className="text-blue-400 font-bold">@</span>
                </div>
                <div className="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-gray-700 transition-colors cursor-pointer">
                  <span className="text-blue-400 font-bold">in</span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-white font-semibold mb-4">Plataforma</h3>
              <ul className="space-y-2 text-gray-400">
                <li><button onClick={() => scrollToSection('jogos')} className="hover:text-white transition-colors">Jogos Suportados</button></li>
                <li><button onClick={() => scrollToSection('para-jogadores')} className="hover:text-white transition-colors">Para Jogadores</button></li>
                <li><button onClick={() => scrollToSection('para-organizadores')} className="hover:text-white transition-colors">Para Organizadores</button></li>
                <li><button onClick={onNavigateToLogin} className="hover:text-white transition-colors">Ver Torneios</button></li>
              </ul>
            </div>

            <div>
              <h3 className="text-white font-semibold mb-4">Suporte</h3>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Central de Ajuda</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Contato</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Termos de Uso</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Política de Privacidade</a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 E-sports PR. Todos os direitos reservados.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}

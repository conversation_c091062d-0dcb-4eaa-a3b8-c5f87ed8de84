/* Landing Page 3D Effects */

.bg-gradient-radial {
  background: radial-gradient(circle, var(--tw-gradient-from), var(--tw-gradient-to));
}

/* 3D Gaming Setup Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(2deg); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
  50% { box-shadow: 0 0 30px rgba(59, 130, 246, 0.6); }
}

@keyframes matrix {
  0% { opacity: 0.2; transform: translateY(0px); }
  50% { opacity: 0.6; transform: translateY(-5px); }
  100% { opacity: 0.2; transform: translateY(0px); }
}

.gaming-element {
  animation: float 6s ease-in-out infinite;
}

.gaming-element:nth-child(2) {
  animation-delay: -2s;
}

.gaming-element:nth-child(3) {
  animation-delay: -4s;
}

.monitor-glow {
  animation: glow 3s ease-in-out infinite;
}

.code-matrix {
  animation: matrix 4s ease-in-out infinite;
}

/* Parallax Layers */
.parallax-layer-1 {
  transform-style: preserve-3d;
}

.parallax-layer-2 {
  transform-style: preserve-3d;
}

.parallax-layer-3 {
  transform-style: preserve-3d;
}

/* 3D Depth Effects */
.depth-1 {
  transform: translateZ(100px);
}

.depth-2 {
  transform: translateZ(50px);
}

.depth-3 {
  transform: translateZ(25px);
}

/* Gaming Particle Effects */
@keyframes particle-float {
  0% { transform: translateY(0px) translateX(0px) scale(1); opacity: 0.8; }
  33% { transform: translateY(-20px) translateX(10px) scale(1.1); opacity: 1; }
  66% { transform: translateY(-10px) translateX(-5px) scale(0.9); opacity: 0.9; }
  100% { transform: translateY(0px) translateX(0px) scale(1); opacity: 0.8; }
}

.gaming-particle {
  animation: particle-float 8s ease-in-out infinite;
}

.gaming-particle:nth-child(odd) {
  animation-delay: -2s;
  animation-duration: 6s;
}

.gaming-particle:nth-child(even) {
  animation-delay: -4s;
  animation-duration: 10s;
}

/* Hover 3D Effects */
.hover-3d {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-3d:hover {
  transform: translateY(-5px) rotateX(5deg) rotateY(5deg);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Text Glow Effects */
.text-glow-blue {
  text-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
}

.text-glow-purple {
  text-shadow: 0 0 20px rgba(147, 51, 234, 0.5);
}

.text-glow-cyan {
  text-shadow: 0 0 20px rgba(6, 182, 212, 0.5);
}

/* Gaming UI Elements */
.gaming-ui-element {
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
}

.gaming-ui-element::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.5), transparent);
}

/* Responsive 3D Effects */
@media (max-width: 768px) {
  .gaming-element {
    animation-duration: 4s;
  }
  
  .hover-3d:hover {
    transform: translateY(-3px) rotateX(2deg) rotateY(2deg);
  }
}

/* Performance Optimizations */
.parallax-container {
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
}

.parallax-element {
  will-change: transform;
  backface-visibility: hidden;
}

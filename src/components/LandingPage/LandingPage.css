/* Landing Page 3D Effects */

.bg-gradient-radial {
  background: radial-gradient(circle, var(--tw-gradient-from), var(--tw-gradient-to));
}

/* Gaming Cursor Effects */
.gaming-cursor {
  cursor: none;
}

.cursor-dot {
  position: fixed;
  top: 0;
  left: 0;
  width: 8px;
  height: 8px;
  background: #3b82f6;
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  transition: transform 0.1s ease;
  box-shadow: 0 0 10px #3b82f6;
}

.cursor-outline {
  position: fixed;
  top: 0;
  left: 0;
  width: 40px;
  height: 40px;
  border: 2px solid rgba(59, 130, 246, 0.5);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9998;
  transition: all 0.15s ease;
  animation: cursor-pulse 2s infinite;
}

@keyframes cursor-pulse {
  0%, 100% {
    transform: scale(1);
    border-color: rgba(59, 130, 246, 0.5);
  }
  50% {
    transform: scale(1.1);
    border-color: rgba(139, 92, 246, 0.8);
  }
}

/* Cursor hover effects */
.cursor-hover .cursor-dot {
  transform: scale(1.5);
  background: #10b981;
  box-shadow: 0 0 20px #10b981;
}

.cursor-hover .cursor-outline {
  transform: scale(1.5);
  border-color: rgba(16, 185, 129, 0.8);
  animation: cursor-hover-pulse 1s infinite;
}

@keyframes cursor-hover-pulse {
  0%, 100% {
    transform: scale(1.5);
    border-color: rgba(16, 185, 129, 0.8);
  }
  50% {
    transform: scale(1.7);
    border-color: rgba(16, 185, 129, 1);
  }
}

/* Cursor click effect */
.cursor-click .cursor-dot {
  transform: scale(0.8);
  background: #ec4899;
  box-shadow: 0 0 30px #ec4899;
}

.cursor-click .cursor-outline {
  transform: scale(2);
  border-color: rgba(236, 72, 153, 1);
  animation: cursor-click-ripple 0.3s ease-out;
}

@keyframes cursor-click-ripple {
  0% {
    transform: scale(1.5);
    opacity: 1;
  }
  100% {
    transform: scale(3);
    opacity: 0;
  }
}

/* Gaming crosshair cursor for buttons */
.cursor-crosshair .cursor-dot {
  width: 4px;
  height: 4px;
  background: #fbbf24;
  box-shadow: 0 0 15px #fbbf24;
}

.cursor-crosshair .cursor-outline {
  width: 30px;
  height: 30px;
  border: 2px solid rgba(251, 191, 36, 0.8);
  border-radius: 0;
  transform: rotate(45deg);
  animation: crosshair-rotate 3s linear infinite;
}

@keyframes crosshair-rotate {
  0% { transform: rotate(45deg); }
  100% { transform: rotate(405deg); }
}

/* Gaming trail effect */
.cursor-trail {
  position: fixed;
  width: 6px;
  height: 6px;
  background: rgba(59, 130, 246, 0.6);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9997;
  animation: trail-fade 0.5s ease-out forwards;
}

@keyframes trail-fade {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0);
  }
}

/* Gaming elements hover effects */
.gaming-element:hover {
  transform: scale(1.05);
  filter: brightness(1.2);
  transition: all 0.3s ease;
}

/* Special cursor effects for gaming elements */
.wasd-key {
  transition: all 0.3s ease;
}

.wasd-key:hover {
  background: rgba(59, 130, 246, 0.3) !important;
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.5);
  transform: scale(1.1);
}

/* Cursor magnetic effect for important buttons */
.cursor-magnetic {
  transition: transform 0.2s ease;
}

.cursor-magnetic:hover {
  transform: scale(1.05);
}

/* Gaming glow effect on hover */
.gaming-glow-hover:hover {
  box-shadow: 0 0 30px rgba(59, 130, 246, 0.4);
  border-color: rgba(59, 130, 246, 0.6);
}

/* Disable cursor on mobile */
@media (max-width: 768px) {
  .gaming-cursor {
    cursor: auto;
  }

  .cursor-dot,
  .cursor-outline,
  .cursor-trail {
    display: none;
  }
}

/* 3D Gaming Setup Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(2deg); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
  50% { box-shadow: 0 0 30px rgba(59, 130, 246, 0.6); }
}

@keyframes matrix {
  0% { opacity: 0.2; transform: translateY(0px); }
  50% { opacity: 0.6; transform: translateY(-5px); }
  100% { opacity: 0.2; transform: translateY(0px); }
}

.gaming-element {
  animation: float 6s ease-in-out infinite;
}

.gaming-element:nth-child(2) {
  animation-delay: -2s;
}

.gaming-element:nth-child(3) {
  animation-delay: -4s;
}

.monitor-glow {
  animation: glow 3s ease-in-out infinite;
}

.code-matrix {
  animation: matrix 4s ease-in-out infinite;
}

/* Parallax Layers */
.parallax-layer-1 {
  transform-style: preserve-3d;
}

.parallax-layer-2 {
  transform-style: preserve-3d;
}

.parallax-layer-3 {
  transform-style: preserve-3d;
}

/* 3D Depth Effects */
.depth-1 {
  transform: translateZ(100px);
}

.depth-2 {
  transform: translateZ(50px);
}

.depth-3 {
  transform: translateZ(25px);
}

/* Gaming Particle Effects */
@keyframes particle-float {
  0% { transform: translateY(0px) translateX(0px) scale(1); opacity: 0.8; }
  33% { transform: translateY(-20px) translateX(10px) scale(1.1); opacity: 1; }
  66% { transform: translateY(-10px) translateX(-5px) scale(0.9); opacity: 0.9; }
  100% { transform: translateY(0px) translateX(0px) scale(1); opacity: 0.8; }
}

.gaming-particle {
  animation: particle-float 8s ease-in-out infinite;
}

.gaming-particle:nth-child(odd) {
  animation-delay: -2s;
  animation-duration: 6s;
}

.gaming-particle:nth-child(even) {
  animation-delay: -4s;
  animation-duration: 10s;
}

/* Hover 3D Effects */
.hover-3d {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-3d:hover {
  transform: translateY(-5px) rotateX(5deg) rotateY(5deg);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Text Glow Effects */
.text-glow-blue {
  text-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
}

.text-glow-purple {
  text-shadow: 0 0 20px rgba(147, 51, 234, 0.5);
}

.text-glow-cyan {
  text-shadow: 0 0 20px rgba(6, 182, 212, 0.5);
}

/* Gaming UI Elements */
.gaming-ui-element {
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
}

.gaming-ui-element::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.5), transparent);
}

/* Responsive 3D Effects */
@media (max-width: 768px) {
  .gaming-element {
    animation-duration: 4s;
  }
  
  .hover-3d:hover {
    transform: translateY(-3px) rotateX(2deg) rotateY(2deg);
  }
}

/* Performance Optimizations */
.parallax-container {
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
}

.parallax-element {
  will-change: transform;
  backface-visibility: hidden;
}

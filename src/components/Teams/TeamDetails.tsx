import React, { useState, useEffect } from 'react';
import { Users, Crown, Calendar, Settings, UserPlus, UserMinus, ArrowLeft, Mail, AlertCircle } from 'lucide-react';
import { apiService } from '../../services/apiService';
import { SendInvite } from './SendInvite';
import { useAuth } from '../../contexts/AuthContext';
import { useToast } from '../../contexts/ToastContext';

interface TeamMember {
  id: string;
  name: string;
  email: string;
}

interface TeamDetailsData {
  id: string;
  name: string;
  game: string;
  logo?: string;
  captainId: string;
  captain: {
    id: string;
    name: string;
    email: string;
  };
  members: TeamMember[];
  joinedAt: string;
  createdAt: string;
}

interface TeamDetailsProps {
  teamId: string;
  onBack: () => void;
}

export function TeamDetails({ teamId, onBack }: TeamDetailsProps) {
  const { user } = useAuth();
  const toast = useToast();
  const [team, setTeam] = useState<TeamDetailsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'members' | 'invite'>('overview');
  const [removingMember, setRemovingMember] = useState<string | null>(null);

  useEffect(() => {
    loadTeamDetails();
  }, [teamId]);

  const loadTeamDetails = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Por enquanto, vamos buscar da lista de times do usuário
      const response = await apiService.getMyTeams();
      
      if (response.success) {
        const foundTeam = response.teams?.find((t: TeamDetailsData) => t.id === teamId);
        if (foundTeam) {
          setTeam(foundTeam);
        } else {
          setError('Time não encontrado');
        }
      } else {
        setError(response.error || 'Erro ao carregar detalhes do time');
      }
    } catch (err) {
      setError('Erro ao carregar detalhes do time');
      console.error('Erro ao carregar detalhes:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveMember = async (memberId: string) => {
    if (!team || !window.confirm('Tem certeza que deseja remover este membro do time?')) {
      return;
    }

    try {
      setRemovingMember(memberId);
      const result = await apiService.removePlayerFromTeam(team.id, memberId);
      
      if (result.success) {
        // Atualizar a lista local
        setTeam(prev => prev ? {
          ...prev,
          members: prev.members.filter(m => m.id !== memberId)
        } : null);
        toast.success('Membro removido do time com sucesso!');
      } else {
        const errorMsg = result.error || 'Erro ao remover membro';
        setError(errorMsg);
        toast.error(errorMsg);
      }
    } catch (err) {
      const errorMsg = 'Erro ao remover membro';
      setError(errorMsg);
      toast.error(errorMsg);
      console.error('Erro ao remover membro:', err);
    } finally {
      setRemovingMember(null);
    }
  };

  const handleInviteSent = () => {
    setActiveTab('overview');
    // Recarregar dados do time para mostrar novos membros
    loadTeamDetails();
  };

  if (loading) {
    return (
      <div className="text-center py-12">
        <div className="w-8 h-8 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <p className="text-gray-400">Carregando detalhes do time...</p>
      </div>
    );
  }

  if (error || !team) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="w-16 h-16 text-red-400 mx-auto mb-4" />
        <p className="text-red-400 mb-4">{error || 'Time não encontrado'}</p>
        <button
          onClick={onBack}
          className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
        >
          Voltar
        </button>
      </div>
    );
  }

  const isCaptain = user?.id === team.captainId;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <button
          onClick={onBack}
          className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
        >
          <ArrowLeft className="w-5 h-5" />
        </button>
        
        <div className="flex items-center space-x-4">
          {team.logo ? (
            <img
              src={team.logo}
              alt={team.name}
              className="w-16 h-16 rounded-xl object-cover"
            />
          ) : (
            <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-blue-500 rounded-xl flex items-center justify-center">
              <Users className="w-8 h-8 text-white" />
            </div>
          )}
          
          <div>
            <div className="flex items-center space-x-3">
              <h2 className="text-2xl font-bold text-white">{team.name}</h2>
              {isCaptain && (
                <Crown className="w-6 h-6 text-yellow-500" title="Você é o capitão" />
              )}
            </div>
            <div className="flex items-center space-x-4 text-gray-400">
              <span>{team.game}</span>
              <span>{team.members.length} membro{team.members.length !== 1 ? 's' : ''}</span>
              <span>Criado em {new Date(team.createdAt).toLocaleDateString('pt-BR')}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-gray-800 p-1 rounded-lg">
        <button
          onClick={() => setActiveTab('overview')}
          className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'overview'
              ? 'bg-purple-600 text-white'
              : 'text-gray-400 hover:text-white hover:bg-gray-700'
          }`}
        >
          <Settings className="w-4 h-4" />
          <span>Visão Geral</span>
        </button>

        <button
          onClick={() => setActiveTab('members')}
          className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'members'
              ? 'bg-purple-600 text-white'
              : 'text-gray-400 hover:text-white hover:bg-gray-700'
          }`}
        >
          <Users className="w-4 h-4" />
          <span>Membros ({team.members.length})</span>
        </button>

        {isCaptain && (
          <button
            onClick={() => setActiveTab('invite')}
            className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'invite'
                ? 'bg-purple-600 text-white'
                : 'text-gray-400 hover:text-white hover:bg-gray-700'
            }`}
          >
            <UserPlus className="w-4 h-4" />
            <span>Convidar</span>
          </button>
        )}
      </div>

      {/* Content */}
      <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-white mb-4">Informações do Time</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm text-gray-400">Nome do Time</label>
                    <p className="text-white font-medium">{team.name}</p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-400">Jogo</label>
                    <p className="text-white font-medium">{team.game}</p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-400">Capitão</label>
                    <p className="text-white font-medium">{team.captain.name}</p>
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm text-gray-400">Membros</label>
                    <p className="text-white font-medium">{team.members.length}/5</p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-400">Criado em</label>
                    <p className="text-white font-medium">{new Date(team.createdAt).toLocaleDateString('pt-BR')}</p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-400">Você entrou em</label>
                    <p className="text-white font-medium">{new Date(team.joinedAt).toLocaleDateString('pt-BR')}</p>
                  </div>
                </div>
              </div>
            </div>

            {isCaptain && (
              <div className="pt-6 border-t border-gray-700">
                <h4 className="text-lg font-semibold text-white mb-4">Ações do Capitão</h4>
                <div className="flex space-x-4">
                  <button
                    onClick={() => setActiveTab('invite')}
                    className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-green-600 to-green-700 text-white rounded-lg hover:from-green-700 hover:to-green-800 transition-all duration-200"
                  >
                    <UserPlus className="w-4 h-4" />
                    <span>Convidar Jogador</span>
                  </button>
                  <button
                    onClick={() => setActiveTab('members')}
                    className="flex items-center space-x-2 px-4 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-700 transition-colors"
                  >
                    <Users className="w-4 h-4" />
                    <span>Gerenciar Membros</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'members' && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-white">Membros do Time</h3>
              <span className="text-gray-400 text-sm">{team.members.length}/5 membros</span>
            </div>

            <div className="space-y-3">
              {team.members.map((member) => (
                <div
                  key={member.id}
                  className="flex items-center justify-between p-4 bg-gray-700 rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
                      <span className="text-white font-medium text-sm">
                        {member.name.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div>
                      <div className="flex items-center space-x-2">
                        <p className="text-white font-medium">{member.name}</p>
                        {member.id === team.captainId && (
                          <Crown className="w-4 h-4 text-yellow-500" />
                        )}
                      </div>
                      <p className="text-gray-400 text-sm">{member.email}</p>
                    </div>
                  </div>

                  {isCaptain && member.id !== team.captainId && (
                    <button
                      onClick={() => handleRemoveMember(member.id)}
                      disabled={removingMember === member.id}
                      className="flex items-center space-x-2 px-3 py-1 text-red-400 hover:bg-red-500/10 rounded-lg transition-colors disabled:opacity-50"
                    >
                      {removingMember === member.id ? (
                        <div className="w-4 h-4 border-2 border-red-400 border-t-transparent rounded-full animate-spin"></div>
                      ) : (
                        <UserMinus className="w-4 h-4" />
                      )}
                      <span className="text-sm">Remover</span>
                    </button>
                  )}
                </div>
              ))}
            </div>

            {team.members.length < 5 && isCaptain && (
              <div className="text-center py-6 border-2 border-dashed border-gray-600 rounded-lg">
                <UserPlus className="w-12 h-12 text-gray-500 mx-auto mb-3" />
                <p className="text-gray-400 mb-3">Ainda há vagas no time</p>
                <button
                  onClick={() => setActiveTab('invite')}
                  className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                >
                  Convidar Jogador
                </button>
              </div>
            )}
          </div>
        )}

        {activeTab === 'invite' && isCaptain && (
          <SendInvite
            teamId={team.id}
            teamName={team.name}
            onInviteSent={handleInviteSent}
            onCancel={() => setActiveTab('overview')}
          />
        )}
      </div>
    </div>
  );
}

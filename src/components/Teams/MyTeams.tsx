import React, { useState, useEffect } from 'react';
import { Users, Crown, Calendar, GamepadIcon, AlertCircle, Settings } from 'lucide-react';
import { apiService } from '../../services/apiService';

interface Team {
  id: string;
  name: string;
  game: string;
  logo?: string;
  captainId: string;
  captain: {
    id: string;
    name: string;
    email: string;
  };
  members: Array<{
    id: string;
    name: string;
    email: string;
  }>;
  joinedAt: string;
  createdAt: string;
}

interface MyTeamsProps {
  onTeamDetails?: (teamId: string) => void;
}

export function MyTeams({ onTeamDetails }: MyTeamsProps) {
  const [teams, setTeams] = useState<Team[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadMyTeams();
  }, []);

  const loadMyTeams = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiService.getMyTeams();
      
      if (response.success) {
        setTeams(response.teams || []);
      } else {
        setError(response.error || 'Erro ao carregar seus times');
      }
    } catch (err) {
      setError('Erro ao carregar seus times');
      console.error('Erro ao carregar times:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="text-center py-12">
        <div className="w-8 h-8 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <p className="text-gray-400">Carregando seus times...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="w-16 h-16 text-red-400 mx-auto mb-4" />
        <p className="text-red-400 mb-4">{error}</p>
        <button
          onClick={loadMyTeams}
          className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
        >
          Tentar Novamente
        </button>
      </div>
    );
  }

  if (teams.length === 0) {
    return (
      <div className="text-center py-12">
        <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-white mb-2">Você não está em nenhum time</h3>
        <p className="text-gray-400 mb-6">
          Crie seu próprio time ou procure times para se juntar
        </p>
        <div className="space-y-2">
          <p className="text-sm text-gray-500">💡 Dica: Você pode estar em um time por jogo</p>
          <p className="text-sm text-gray-500">🎮 Exemplo: Um time de CS2 e outro de Valorant</p>
        </div>
      </div>
    );
  }

  // Agrupar times por jogo
  const teamsByGame = teams.reduce((acc, team) => {
    if (!acc[team.game]) {
      acc[team.game] = [];
    }
    acc[team.game].push(team);
    return acc;
  }, {} as Record<string, Team[]>);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-xl font-semibold text-white">Meus Times</h3>
        <span className="text-gray-400 text-sm">
          {teams.length} time{teams.length !== 1 ? 's' : ''} • {Object.keys(teamsByGame).length} jogo{Object.keys(teamsByGame).length !== 1 ? 's' : ''}
        </span>
      </div>

      {Object.entries(teamsByGame).map(([game, gameTeams]) => (
        <div key={game} className="space-y-4">
          <div className="flex items-center space-x-3">
            <GamepadIcon className="w-5 h-5 text-purple-500" />
            <h4 className="text-lg font-medium text-white">{game}</h4>
            <span className="text-sm text-gray-500">
              ({gameTeams.length} time{gameTeams.length !== 1 ? 's' : ''})
            </span>
          </div>

          <div className="grid gap-4">
            {gameTeams.map((team) => {
              const isCaptain = team.captain.id === team.captainId;
              
              return (
                <div
                  key={team.id}
                  className="bg-gray-700 rounded-xl p-6 border border-gray-600"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-4">
                      {team.logo ? (
                        <img
                          src={team.logo}
                          alt={team.name}
                          className="w-12 h-12 rounded-lg object-cover"
                        />
                      ) : (
                        <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
                          <Users className="w-6 h-6 text-white" />
                        </div>
                      )}
                      
                      <div>
                        <div className="flex items-center space-x-2">
                          <h5 className="text-lg font-semibold text-white">
                            {team.name}
                          </h5>
                          {isCaptain && (
                            <Crown className="w-4 h-4 text-yellow-500" title="Você é o capitão" />
                          )}
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-gray-400">
                          <span>Capitão: {team.captain.name}</span>
                          <span>{team.members.length} membro{team.members.length !== 1 ? 's' : ''}</span>
                        </div>
                      </div>
                    </div>

                    <div className="text-right text-sm text-gray-500">
                      <div>Entrou em</div>
                      <div>{new Date(team.joinedAt).toLocaleDateString('pt-BR')}</div>
                    </div>
                  </div>

                  {team.members.length > 0 && (
                    <div className="pt-4 border-t border-gray-600">
                      <div className="text-sm text-gray-400 mb-3">Membros do time:</div>
                      <div className="flex flex-wrap gap-2">
                        {team.members.map((member) => (
                          <span
                            key={member.id}
                            className={`px-3 py-1 rounded-full text-xs ${
                              member.id === team.captainId
                                ? 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30'
                                : 'bg-gray-600 text-gray-300'
                            }`}
                          >
                            {member.name}
                            {member.id === team.captainId && ' (Capitão)'}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="mt-4 pt-4 border-t border-gray-600">
                    <div className="flex items-center justify-between">
                      {isCaptain && (
                        <div className="flex items-center space-x-2 text-sm text-blue-400">
                          <Crown className="w-4 h-4" />
                          <span>Você é o capitão deste time</span>
                        </div>
                      )}

                      <button
                        onClick={() => onTeamDetails?.(team.id)}
                        className="flex items-center space-x-2 px-3 py-1 text-purple-400 hover:bg-purple-500/10 rounded-lg transition-colors text-sm"
                      >
                        <Settings className="w-4 h-4" />
                        <span>Detalhes</span>
                      </button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      ))}

      <div className="mt-8 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
        <h4 className="text-sm font-medium text-blue-400 mb-2">📋 Regras dos Times:</h4>
        <ul className="text-xs text-blue-300 space-y-1">
          <li>• Você pode estar em apenas um time por jogo</li>
          <li>• Você pode estar em times de jogos diferentes simultaneamente</li>
          <li>• Como capitão, você pode gerenciar membros do seu time</li>
          <li>• Times são necessários para participar de torneios em equipe</li>
        </ul>
      </div>
    </div>
  );
}

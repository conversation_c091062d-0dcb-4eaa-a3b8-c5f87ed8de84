import React, { useState, useEffect } from 'react';
import { Trophy, Users, Target, TrendingUp, Calendar, Award } from 'lucide-react';
import { apiService } from '../../services/apiService';

interface TeamStats {
  totalTeams: number;
  teamsByGame: Record<string, number>;
  totalTournaments: number;
  activeTournaments: number;
  completedTournaments: number;
  achievements: number;
  winRate: number;
  totalMatches: number;
  wins: number;
  losses: number;
  averageTeamSize: number;
  oldestTeam: {
    name: string;
    createdAt: string;
    game: string;
  } | null;
  newestTeam: {
    name: string;
    createdAt: string;
    game: string;
  } | null;
}

export function TeamStats() {
  const [stats, setStats] = useState<TeamStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Buscar times do usuário
      const teamsResponse = await apiService.getMyTeams();
      const userStatsResponse = await apiService.getUserStats();
      
      if (teamsResponse.success && userStatsResponse.success) {
        const teams = teamsResponse.teams || [];
        const userStats = userStatsResponse.stats || {};
        
        // Agrupar times por jogo
        const teamsByGame = teams.reduce((acc: Record<string, number>, team: any) => {
          acc[team.game] = (acc[team.game] || 0) + 1;
          return acc;
        }, {});

        // Calcular estatísticas avançadas
        const totalMembers = teams.reduce((sum: number, team: any) => sum + (team.members?.length || 0), 0);
        const averageTeamSize = teams.length > 0 ? totalMembers / teams.length : 0;

        // Encontrar time mais antigo e mais novo
        const sortedByDate = [...teams].sort((a: any, b: any) =>
          new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        );

        const oldestTeam = sortedByDate.length > 0 ? {
          name: sortedByDate[0].name,
          createdAt: sortedByDate[0].createdAt,
          game: sortedByDate[0].game
        } : null;

        const newestTeam = sortedByDate.length > 0 ? {
          name: sortedByDate[sortedByDate.length - 1].name,
          createdAt: sortedByDate[sortedByDate.length - 1].createdAt,
          game: sortedByDate[sortedByDate.length - 1].game
        } : null;

        const calculatedStats: TeamStats = {
          totalTeams: teams.length,
          teamsByGame,
          totalTournaments: userStats.totalTournaments || 0,
          activeTournaments: userStats.activeTournaments || 0,
          completedTournaments: (userStats.totalTournaments || 0) - (userStats.activeTournaments || 0),
          achievements: userStats.achievements || 0,
          winRate: userStats.winRate || 0,
          totalMatches: userStats.totalMatches || 0,
          wins: userStats.wins || 0,
          losses: userStats.losses || 0,
          averageTeamSize,
          oldestTeam,
          newestTeam
        };

        setStats(calculatedStats);
      } else {
        setError('Erro ao carregar estatísticas');
      }
    } catch (err) {
      setError('Erro ao carregar estatísticas');
      console.error('Erro ao carregar stats:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
        <div className="text-center py-8">
          <div className="w-8 h-8 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-400">Carregando estatísticas...</p>
        </div>
      </div>
    );
  }

  if (error || !stats) {
    return (
      <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
        <div className="text-center py-8">
          <p className="text-red-400 mb-4">{error || 'Erro ao carregar estatísticas'}</p>
          <button
            onClick={loadStats}
            className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            Tentar Novamente
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-xl font-semibold text-white mb-4">Estatísticas dos Times</h3>
      </div>

      {/* Cards de Estatísticas Principais */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-gradient-to-br from-purple-600 to-purple-700 rounded-xl p-4 text-white">
          <div className="flex items-center space-x-3">
            <Users className="w-8 h-8" />
            <div>
              <div className="text-2xl font-bold">{stats.totalTeams}</div>
              <div className="text-purple-100 text-sm">Times</div>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl p-4 text-white">
          <div className="flex items-center space-x-3">
            <Trophy className="w-8 h-8" />
            <div>
              <div className="text-2xl font-bold">{stats.totalTournaments}</div>
              <div className="text-blue-100 text-sm">Torneios</div>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-green-600 to-green-700 rounded-xl p-4 text-white">
          <div className="flex items-center space-x-3">
            <Calendar className="w-8 h-8" />
            <div>
              <div className="text-2xl font-bold">{stats.wins}</div>
              <div className="text-green-100 text-sm">Vitórias</div>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-yellow-600 to-yellow-700 rounded-xl p-4 text-white">
          <div className="flex items-center space-x-3">
            <Award className="w-8 h-8" />
            <div>
              <div className="text-2xl font-bold">{stats.achievements}</div>
              <div className="text-yellow-100 text-sm">Conquistas</div>
            </div>
          </div>
        </div>
      </div>

      {/* Cards Secundários */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-gray-800 rounded-xl p-4 border border-gray-700">
          <div className="text-center">
            <div className="text-xl font-bold text-white">{stats.averageTeamSize.toFixed(1)}</div>
            <div className="text-gray-400 text-sm">Média de Membros</div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-xl p-4 border border-gray-700">
          <div className="text-center">
            <div className="text-xl font-bold text-white">{stats.completedTournaments}</div>
            <div className="text-gray-400 text-sm">Concluídos</div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-xl p-4 border border-gray-700">
          <div className="text-center">
            <div className="text-xl font-bold text-white">{stats.losses}</div>
            <div className="text-gray-400 text-sm">Derrotas</div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-xl p-4 border border-gray-700">
          <div className="text-center">
            <div className="text-xl font-bold text-white">{stats.totalMatches}</div>
            <div className="text-gray-400 text-sm">Total Partidas</div>
          </div>
        </div>
      </div>

      {/* Times por Jogo */}
      {Object.keys(stats.teamsByGame).length > 0 && (
        <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
          <h4 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
            <Target className="w-5 h-5 text-purple-500" />
            <span>Times por Jogo</span>
          </h4>
          
          <div className="space-y-3">
            {Object.entries(stats.teamsByGame).map(([game, count]) => (
              <div key={game} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                  <span className="text-white font-medium">{game}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-gray-400">{count} time{count !== 1 ? 's' : ''}</span>
                  <div className="w-16 bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${(count / Math.max(...Object.values(stats.teamsByGame))) * 100}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Performance */}
      <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
        <h4 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
          <TrendingUp className="w-5 h-5 text-green-500" />
          <span>Performance</span>
        </h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <div className="text-sm text-gray-400 mb-2">Taxa de Vitória</div>
            <div className="flex items-center space-x-3">
              <div className="flex-1 bg-gray-700 rounded-full h-3">
                <div 
                  className="bg-gradient-to-r from-green-500 to-green-400 h-3 rounded-full transition-all duration-500"
                  style={{ width: `${stats.winRate}%` }}
                ></div>
              </div>
              <span className="text-white font-medium">{stats.winRate.toFixed(1)}%</span>
            </div>
          </div>

          <div>
            <div className="text-sm text-gray-400 mb-2">Torneios Ativos</div>
            <div className="flex items-center space-x-3">
              <div className="flex-1 bg-gray-700 rounded-full h-3">
                <div 
                  className="bg-gradient-to-r from-blue-500 to-blue-400 h-3 rounded-full transition-all duration-500"
                  style={{ 
                    width: `${stats.totalTournaments > 0 ? (stats.activeTournaments / stats.totalTournaments) * 100 : 0}%` 
                  }}
                ></div>
              </div>
              <span className="text-white font-medium">
                {stats.activeTournaments}/{stats.totalTournaments}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Histórico de Times */}
      {(stats.oldestTeam || stats.newestTeam) && (
        <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
          <h4 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
            <Calendar className="w-5 h-5 text-blue-500" />
            <span>Histórico de Times</span>
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {stats.oldestTeam && (
              <div className="space-y-2">
                <div className="text-sm text-gray-400">Time mais antigo</div>
                <div className="p-3 bg-gray-700 rounded-lg">
                  <div className="font-medium text-white">{stats.oldestTeam.name}</div>
                  <div className="text-sm text-gray-400">{stats.oldestTeam.game}</div>
                  <div className="text-xs text-gray-500">
                    {new Date(stats.oldestTeam.createdAt).toLocaleDateString('pt-BR')}
                  </div>
                </div>
              </div>
            )}

            {stats.newestTeam && (
              <div className="space-y-2">
                <div className="text-sm text-gray-400">Time mais recente</div>
                <div className="p-3 bg-gray-700 rounded-lg">
                  <div className="font-medium text-white">{stats.newestTeam.name}</div>
                  <div className="text-sm text-gray-400">{stats.newestTeam.game}</div>
                  <div className="text-xs text-gray-500">
                    {new Date(stats.newestTeam.createdAt).toLocaleDateString('pt-BR')}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Resumo */}
      <div className="bg-gradient-to-r from-purple-600/10 to-blue-600/10 border border-purple-500/20 rounded-xl p-6">
        <h4 className="text-lg font-semibold text-white mb-3">📊 Resumo Geral</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div className="space-y-2">
            <p className="text-gray-300">
              • Você está em <span className="text-purple-400 font-medium">{stats.totalTeams}</span> time{stats.totalTeams !== 1 ? 's' : ''}
            </p>
            <p className="text-gray-300">
              • Participou de <span className="text-blue-400 font-medium">{stats.totalTournaments}</span> torneio{stats.totalTournaments !== 1 ? 's' : ''}
            </p>
            <p className="text-gray-300">
              • Média de <span className="text-cyan-400 font-medium">{stats.averageTeamSize.toFixed(1)}</span> membros por time
            </p>
          </div>
          <div className="space-y-2">
            <p className="text-gray-300">
              • <span className="text-green-400 font-medium">{stats.wins}</span> vitória{stats.wins !== 1 ? 's' : ''} • <span className="text-red-400 font-medium">{stats.losses}</span> derrota{stats.losses !== 1 ? 's' : ''}
            </p>
            <p className="text-gray-300">
              • Taxa de vitória: <span className="text-yellow-400 font-medium">{stats.winRate.toFixed(1)}%</span>
            </p>
            <p className="text-gray-300">
              • <span className="text-yellow-400 font-medium">{stats.achievements}</span> conquista{stats.achievements !== 1 ? 's' : ''} desbloqueada{stats.achievements !== 1 ? 's' : ''}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

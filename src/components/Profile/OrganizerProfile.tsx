import React, { useState, useEffect } from 'react';
import { 
  User, Building, Globe, Twitter, Instagram, MessageCircle, 
  Save, Edit, Camera, Mail, Phone, MapPin, Calendar,
  AlertCircle, CheckCircle, Loader
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useToast } from '../../contexts/ToastContext';
import { apiService } from '../../services/apiService';

interface OrganizerProfileData {
  name: string;
  email: string;
  company: string;
  website: string;
  bio: string;
  socialMedia: {
    twitter: string;
    instagram: string;
    discord: string;
    linkedin: string;
  };
  avatar: string;
}

export function OrganizerProfile() {
  const { user } = useAuth();
  const toast = useToast();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [editing, setEditing] = useState(false);
  
  const [profileData, setProfileData] = useState<OrganizerProfileData>({
    name: user?.name || '',
    email: user?.email || '',
    company: '',
    website: '',
    bio: '',
    socialMedia: {
      twitter: '',
      instagram: '',
      discord: '',
      linkedin: ''
    },
    avatar: user?.avatar || ''
  });

  useEffect(() => {
    loadProfile();
  }, []);

  const loadProfile = async () => {
    try {
      setLoading(true);
      const response = await apiService.getUserProfile();
      
      if (response.success && response.user) {
        setProfileData({
          name: response.user.name || '',
          email: response.user.email || '',
          company: response.user.company || '',
          website: response.user.website || '',
          bio: response.user.bio || '',
          socialMedia: response.user.socialMedia || {
            twitter: '',
            instagram: '',
            discord: '',
            linkedin: ''
          },
          avatar: response.user.avatar || ''
        });
      }
    } catch (error) {
      console.error('Erro ao carregar perfil:', error);
      toast.error('Erro ao carregar dados do perfil');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);

      console.log('🔧 Salvando perfil...');
      console.log('📝 Dados a serem salvos:', {
        company: profileData.company,
        website: profileData.website,
        bio: profileData.bio,
        socialMedia: profileData.socialMedia
      });

      const response = await apiService.updateUserProfile({
        company: profileData.company,
        website: profileData.website,
        bio: profileData.bio,
        socialMedia: profileData.socialMedia
      });

      console.log('📡 Resposta da API:', response);

      if (response.success) {
        toast.success('Perfil atualizado com sucesso!');
        setEditing(false);
      } else {
        console.error('❌ Erro na resposta:', response.error);
        toast.error(response.error || 'Erro ao atualizar perfil');
      }
    } catch (error) {
      console.error('❌ Erro ao salvar perfil:', error);
      toast.error('Erro ao salvar perfil');
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    setEditing(false);
    loadProfile(); // Recarrega os dados originais
  };

  if (loading) {
    return (
      <div className="text-center py-12">
        <Loader className="w-8 h-8 animate-spin text-purple-500 mx-auto mb-4" />
        <p className="text-gray-400">Carregando perfil...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Meu Perfil</h2>
          <p className="text-gray-400">Gerencie suas informações de organizador</p>
        </div>
        
        {!editing ? (
          <button
            onClick={() => setEditing(true)}
            className="flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            <Edit className="w-4 h-4" />
            <span>Editar Perfil</span>
          </button>
        ) : (
          <div className="flex space-x-2">
            <button
              onClick={handleCancel}
              className="px-4 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-700 transition-colors"
            >
              Cancelar
            </button>
            <button
              onClick={handleSave}
              disabled={saving}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
            >
              {saving ? (
                <Loader className="w-4 h-4 animate-spin" />
              ) : (
                <Save className="w-4 h-4" />
              )}
              <span>{saving ? 'Salvando...' : 'Salvar'}</span>
            </button>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Coluna da Esquerda - Avatar e Info Básica */}
        <div className="space-y-6">
          {/* Avatar */}
          <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
            <div className="text-center">
              <div className="relative inline-block">
                {profileData.avatar ? (
                  <img
                    src={profileData.avatar}
                    alt={profileData.name}
                    className="w-24 h-24 rounded-full object-cover mx-auto"
                  />
                ) : (
                  <div className="w-24 h-24 bg-gradient-to-br from-purple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto">
                    <User className="w-12 h-12 text-white" />
                  </div>
                )}
                
                {editing && (
                  <button className="absolute bottom-0 right-0 p-2 bg-purple-600 text-white rounded-full hover:bg-purple-700 transition-colors">
                    <Camera className="w-4 h-4" />
                  </button>
                )}
              </div>
              
              <h3 className="text-xl font-semibold text-white mt-4">{profileData.name}</h3>
              <p className="text-gray-400">{profileData.email}</p>
              <div className="mt-2">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-500/20 text-purple-400">
                  Organizador Verificado
                </span>
              </div>
            </div>
          </div>

          {/* Estatísticas Rápidas */}
          <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
            <h4 className="text-lg font-semibold text-white mb-4">Estatísticas</h4>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-400">Torneios Criados</span>
                <span className="text-white font-medium">3</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Participantes Total</span>
                <span className="text-white font-medium">45</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Premiação Distribuída</span>
                <span className="text-green-400 font-medium">R$ 15.000</span>
              </div>
            </div>
          </div>
        </div>

        {/* Coluna da Direita - Formulário */}
        <div className="lg:col-span-2 space-y-6">
          {/* Informações da Empresa */}
          <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
            <h4 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
              <Building className="w-5 h-5 text-purple-500" />
              <span>Informações da Organização</span>
            </h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Nome da Empresa/Organização
                </label>
                {editing ? (
                  <input
                    type="text"
                    value={profileData.company}
                    onChange={(e) => setProfileData({ ...profileData, company: e.target.value })}
                    className="w-full px-4 py-3 bg-gray-900 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="Ex: GameMaster Events"
                  />
                ) : (
                  <p className="text-white py-3">{profileData.company || 'Não informado'}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Website
                </label>
                {editing ? (
                  <input
                    type="url"
                    value={profileData.website}
                    onChange={(e) => setProfileData({ ...profileData, website: e.target.value })}
                    className="w-full px-4 py-3 bg-gray-900 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="https://suaempresa.com.br"
                  />
                ) : (
                  <div className="py-3">
                    {profileData.website ? (
                      <a
                        href={profileData.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-400 hover:text-blue-300 transition-colors flex items-center space-x-2"
                      >
                        <Globe className="w-4 h-4" />
                        <span>{profileData.website}</span>
                      </a>
                    ) : (
                      <p className="text-gray-400">Não informado</p>
                    )}
                  </div>
                )}
              </div>
            </div>

            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Biografia/Descrição
              </label>
              {editing ? (
                <textarea
                  value={profileData.bio}
                  onChange={(e) => setProfileData({ ...profileData, bio: e.target.value })}
                  rows={4}
                  className="w-full px-4 py-3 bg-gray-900 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none"
                  placeholder="Conte sobre sua empresa, experiência em organização de torneios, especialidades..."
                />
              ) : (
                <p className="text-white py-3 whitespace-pre-wrap">
                  {profileData.bio || 'Nenhuma descrição adicionada'}
                </p>
              )}
            </div>
          </div>

          {/* Redes Sociais */}
          <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
            <h4 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
              <MessageCircle className="w-5 h-5 text-blue-500" />
              <span>Redes Sociais</span>
            </h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Twitter
                </label>
                {editing ? (
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">@</span>
                    <input
                      type="text"
                      value={profileData.socialMedia.twitter}
                      onChange={(e) => setProfileData({
                        ...profileData,
                        socialMedia: { ...profileData.socialMedia, twitter: e.target.value }
                      })}
                      className="w-full pl-8 pr-4 py-3 bg-gray-900 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                      placeholder="seu_usuario"
                    />
                  </div>
                ) : (
                  <div className="py-3">
                    {profileData.socialMedia.twitter ? (
                      <a
                        href={`https://twitter.com/${profileData.socialMedia.twitter}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-400 hover:text-blue-300 transition-colors flex items-center space-x-2"
                      >
                        <Twitter className="w-4 h-4" />
                        <span>@{profileData.socialMedia.twitter}</span>
                      </a>
                    ) : (
                      <p className="text-gray-400">Não informado</p>
                    )}
                  </div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Instagram
                </label>
                {editing ? (
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">@</span>
                    <input
                      type="text"
                      value={profileData.socialMedia.instagram}
                      onChange={(e) => setProfileData({
                        ...profileData,
                        socialMedia: { ...profileData.socialMedia, instagram: e.target.value }
                      })}
                      className="w-full pl-8 pr-4 py-3 bg-gray-900 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                      placeholder="seu_usuario"
                    />
                  </div>
                ) : (
                  <div className="py-3">
                    {profileData.socialMedia.instagram ? (
                      <a
                        href={`https://instagram.com/${profileData.socialMedia.instagram}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-pink-400 hover:text-pink-300 transition-colors flex items-center space-x-2"
                      >
                        <Instagram className="w-4 h-4" />
                        <span>@{profileData.socialMedia.instagram}</span>
                      </a>
                    ) : (
                      <p className="text-gray-400">Não informado</p>
                    )}
                  </div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Discord
                </label>
                {editing ? (
                  <input
                    type="url"
                    value={profileData.socialMedia.discord}
                    onChange={(e) => setProfileData({
                      ...profileData,
                      socialMedia: { ...profileData.socialMedia, discord: e.target.value }
                    })}
                    className="w-full px-4 py-3 bg-gray-900 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="https://discord.gg/seuservidor"
                  />
                ) : (
                  <div className="py-3">
                    {profileData.socialMedia.discord ? (
                      <a
                        href={profileData.socialMedia.discord}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-indigo-400 hover:text-indigo-300 transition-colors flex items-center space-x-2"
                      >
                        <MessageCircle className="w-4 h-4" />
                        <span>Discord Server</span>
                      </a>
                    ) : (
                      <p className="text-gray-400">Não informado</p>
                    )}
                  </div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  LinkedIn
                </label>
                {editing ? (
                  <input
                    type="url"
                    value={profileData.socialMedia.linkedin}
                    onChange={(e) => setProfileData({
                      ...profileData,
                      socialMedia: { ...profileData.socialMedia, linkedin: e.target.value }
                    })}
                    className="w-full px-4 py-3 bg-gray-900 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="https://linkedin.com/in/seuperfil"
                  />
                ) : (
                  <div className="py-3">
                    {profileData.socialMedia.linkedin ? (
                      <a
                        href={profileData.socialMedia.linkedin}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-500 transition-colors flex items-center space-x-2"
                      >
                        <Building className="w-4 h-4" />
                        <span>LinkedIn</span>
                      </a>
                    ) : (
                      <p className="text-gray-400">Não informado</p>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Dica */}
          <div className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
            <div className="flex items-start space-x-3">
              <AlertCircle className="w-5 h-5 text-blue-400 mt-0.5" />
              <div>
                <p className="text-blue-400 text-sm font-medium">Dica Importante</p>
                <p className="text-blue-300 text-sm mt-1">
                  Essas informações aparecerão na aba "Organizador" dos seus torneios. 
                  Preencha com dados profissionais para passar mais credibilidade aos participantes.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
